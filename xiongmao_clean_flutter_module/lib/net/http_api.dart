//网络请求的 Api
class HttpApi {
  static const String init = "/init";
  static const String login = "/verify";

  //获取全部的岗位列表
  static const String GET_WORK_MANAGER_LIST = "/company/job/getList";

  //删除预制的岗位
  static const String DELETE_WORK = "/company/job/delete";

  //编辑和新建岗位
  static const String WORK_POST_SAVE = "/company/job/save";

  //模板添加和编辑
  static const String EDIT_CONTRACT_TEMPALTE = "/contract/contractTemplate/save";
  static const String DELETE_CONTRACT_TEMPALTE = "/contract/contractTemplate/delete";

  //获取用工规则的接口
  static const String WORK_RULES_INFO = "/company/setting/getOne";

  //更新用工规则的接口
  static const String SAVE_WORK_RULES_INFO = "/company/setting/save";

  //获取电子合同份数
  static const String GET_CONTRACT_INFO = "/product/contract/getAll";

  //查询合同企业信息
  static const String GET_CONRRACT_ONE = "/contract/contractCustomer/getOne";

  ///获取企业电子签设置
  static const String GET_ELECTRONIC_CONTRACT = "/company/setting/getElectronicContract";

  ///个人获取企业电子签 是否开启
  static const String GET_ELECTRONIC_CONTRACT_PAGE = "/user/contract/isCanElectronic";

  //获取合同的三方注册的 id
  static const String GET_CONTRACT_REGISTER = "/contract/contractCustomer/register";

  //获取电子合同的 url
  static const String GET_VERIFY_URL = "/contract/contractCustomer/getVerifyUrl";

  //自动签署授权
  static const String GET_AUTO_SIGN = "/contract/contractCustomer/beforeAuthSign";

  //获取合同列表
  static const String GET_CONTRACT_LIST = "/contract/contractTemplate/getList";

  //获取合同标签
  static const String GET_CONTRACT_TAG_LIST = "/contract/contractLabel/getList";

  //编辑合同入离职装哎
  static const String GET_CONTRACT_UPDATE_STATUS = "/contract/contractCustomer/save";

  //获取标签详情
  static const String GET_CONTRACT_TAG_ONE = "/contract/contractLabel/getOne";

  //电子合同使用记录
  static const String GET_CONTRACT_HISTORY_LIST = "/company/contractOrder/getConsumeList";

  //编辑印章
  static const String ADD_SIGNATURE = "/contract/contractCustomer/addSignature";

  //【APP】审批模板列表
  static const String GET_APPROVE_TEMPLATE_LIST = "/user/approve/getManageTemplateList";

  // 获取审批模版列表
  static const String GET_APPROVE_RECORD_TEMPLATE_LIST = "/user/approve/getList";

  //【APP】审批模板详情
  static const String GET_APPROVE_DETAIL = "/user/approve/getOneTemplate";

  //【APP】获取企业所有角色
  static const String GET_ROLE_ALL = "/company/role/getAll";

  //获取元数据
  static const String GET_META_DATA = "/data/metadata/getList";

  ///获取城市的City
  static const String GET_CITY_META_DATA = "/data/city/getTree";

  ///获取民族的列表
  static const String GET_META_DATA_NATION = "/data/nation/getList";

  //【APP】审批模板添加和编辑接口
  static const String SAVE_TEMPLATE = "/user/approve/saveTemplate";

  //获取七牛的 token
  static const String GET_QINIU_TOKEN = "/data/oss/getQiniuToken";

  //获取余额
  static const String GET_USER_BALANCE = "/company/balance/getOne";

  ///获取余额流水记录
  static const String GET_USER_BALANCE_BILL_LIST = "/company/balance/getBillList";

  ///提现接口
  static const String GET_USER_BALANCE_DRAW_CASH = "/company/balance/drawCash";

  ///对公充值
  static const String GET_USER_BALANCE_RECHARGE_PRE = "/company/balance/getPublicRechargePreInfo";

  ///获取收款列表账号
  static const String GET_USER_BALANCE_ACCOUNT = "/company/collectionAccount/getList";

  ///收款账号删除
  static const String GET_USER_BALANCE_ACCOUNT_DEL = "/company/collectionAccount/delete";

  ///新增收款账号
  static const String GET_USER_BALANCE_ADD_ACCOUNT = "/company/collectionAccount/create";

  ///获取提现银行
  static const String GET_USER_BALANCE_BANK_LIST = "/data/bank/getList";

  ///获取验证码
  static const String GET_USER_BALANCE_ACCOUNT_CODE = "/data/verificationCode/get";

  ///清洁区域 - 列表
  static const String GET_CLEAN_AREA_LIST = "/project/cleanArea/getList";

  ///清洁区域 - 状态保存
  static const String GET_CLEAN_AREA_SAVE = "/project/cleanArea/save";

  ///清洁区域 - 删除
  static const String GET_CLEAN_AREA_DEL = "/project/cleanArea/delete";

  ///清洁计划列表
  static const String GET_CLEAN_PLAN_LIST_ALL = "/project/plan/getList";

  ///清洁计划添加-编辑
  static const String GET_CLEAN_PLAN_SAVE = "/project/plan/save";

  ///清洁计划 - 获取详情
  static const String GET_CLEAN_PLAN_ONE = "/project/plan/getOne";

  ///清洁计划 - 获取任务详情
  static const String GET_CLEAN_PLAN_TASK_LIST = "/project/plan/getTaskList";

  ///清洁计划添加-删除
  static const String GET_CLEAN_PLAN_DEL = "/project/plan/delete";

  ///清洁计划添加-任务的启动暂停
  static const String GET_CLEAN_PLAN_UPDATE_STATUS = "/project/plan/updateStatus";

  ///清洁计划详情 结果图删除
  static const String GET_CLEAN_PLAN_PIC_DEL = "/project/plan/deleteTaskDealPic";

  ///完成任务
  static const String FINISH_TASK = "/project/plan/finishTask";

  ///工单添加编辑
  static const String GET_WORK_ORDER_SAVE = "/project/workOrder/save";

  ///图片自动识别
  static const String BANK_OCR = "/data/ocr/bankCardRecognition";

  ///获取企业流水类型列表
  static const String BALANCE_TYPE_LIST = "/data/bill/getBalanceTypeList";

  ///获取信用查询的列表数据
  static const String CREDIT_SEARCH_RESULT = "/user/credit/search";

  ///查询用户信用
  static const String CREDIT_RESULT = "/user/credit/getResult";

  ///合同列表
  static const String CONTACT_MY_LIST = "/user/contract/getList";

  ///获取 任务详情
  static const String CONTRCT_ONE_TASK = "/project/plan/getOneTask";

  ///删除我的合同
  static const String CONTRCT_DELETE_MY = "/user/contract/delete";

  ///获取参保方案
  static const String GET_INSURE_SCHEME_ALL = "/product/insurance/getList";

  ///设置参保
  static const String GET_INSURE_SCHEME_SETTING = "/project/insurance/set";

  ///获取企业的详情信息
  static const String GET_COMPANY_BASE_ONE = "/company/base/getOne";

  ///获取保险的列表
  static const String GET_INSURE_SCHEME_HISTORY_LIST = "/insurance/order/getList";

  ///获取保险的详情
  static const String GET_INSURE_SCHEME_HISTORY_ONE = "/insurance/order/getOne";

  ///被保人的操作记录
  static const String GET_INSURE_SCHEME_HISTORY_ACTION_RECORD = "/insurance/operateLog/getList";

  ///系统的消息通知
  static const String GET_NOTICE_MESSAGE_GETLIST = "/user/notice/getList";

  ///设置已读
  static const String GET_NOTICE_MESSAGE_READ = "/user/notice/setRead";

  ///获取全部档案人员 可根据条件筛选
  static const String GET_ARCHIVES_ALL_LIST = "/user/archives/getPersonList";
  ///获取请假
  static const String GET_CAN_OVERTIME_HOLIDAY_LIST = "/user/archives/getCanOvertimeHolidayList";

  ///获取员工列表 一定要区分 员工跟花名册（档案）列表
  static const String GET_STAFF_ALL_LIST = "/user/archives/getList";

  ///获取到期项目列表
  static const String GET_PROJECT_GET_LIST_EXPIRED = "/project/project/getExpiredList";

  ///自动投保
  static const String AUTO_INSURE_BUY = "/company/insurance/buy";

  ///手动更新执行更新脚本 劳动协议、离职协议
  static const String UPDATE_LACK_CONTRACT = "/user/archives/updateLackContract";

  ///当月被投诉的项目列表
  static const String GET_PROJECT_GET_LIST_COMPLAINT = "/project/project/getComplaintList";

  ///无工单
  static const String GET_PROJECT_GET_LIST_WORK_ORDER = "/project/project/getNoWorkOrderList";

  ///逾期项目列表
  static const String GET_PROJECT_GET_LIST_DELAY = "/project/project/getPostponeList";

  ///缺少离职合同的列表
  static const String GET_ARCHIVES_LACK_CONTRACT_LIST = "/user/archives/getLackContractList";

  ///获取工作圈列表
  static const String GET_WORK_CIRCLE_LIST = "/user/workCircle/getList";

  ///物料列表
  static const String GET_CATEGORY_LIST = "/material/category/getList";

  ///物料子列表
  static const String GET_CATEGORY_CHILD_LIST = "/material/sku/getList";

  ///获取审批列表的ICON
  static const String GET_APPROVE_ICON_ALL = "/data/approveTemplate/getIconList";

  ///获取所有的角色
  static const String GET_COMPANY_ROLE_ALL = "/company/role/getAll";

  ///获取所有项目的列表
  static const String GET_PROJECT_MANGER_ALL = "/project/project/getAll";

  ///获取 项目审批详情的
  static const String GET_APPROVE_TEMPLATE_ONE = "/user/approve/getOneTemplate";

  ///自定义审批的提交
  static const String CREATE_APPROVE_TEMPLATE = "/user/approve/createCustom";

  ///获取审批详情
  static const String GET_APPROVE_ONE = "/user/approve/getOne";

  ///提交物料申请
  static const String CREATE_METERIAL = "/user/approve/createMaterial";

  ///可申请的物料cat
  static const String GET_APPLY_CAT = "/material/category/getList";

  ///Sku删除
  static const String APPROVE_SKU_DELETE = "/material/projectSku/delete";

  ///删除SKUALL
  static const String APPROVE_SKU_DELETE_ALL = "/material/projectSku/cleanUp";

  ///增加SKU
  static const String APPROVE_SKU_ADD = "/material/projectSku/append";

  ///模版删除
  static const String APPROVE_TEMPLATE_DEL = "/user/approve/deleteTemplate";

  ///项目物料分类列表
  static const String SKU_PROJECT_CAT_LIST = "/material/category/getProjectCategoryList";
  static const String SKU_PROJECT_CAT_DETAILS_LIST = "/material/projectSku/getList";

  ///客户管理
  static const String GET_CUSTOM_MANGET_LIST = "/company/custom/getList";

  ///客户操作保存
  static const String SAVE_CUSTOM_INFO = "/company/custom/save";

  ///客户操作信息
  static const String CUSTOM_INFO_ONE = "/company/custom/getOne";

  ///获取项目的列表
  static const String PROJECT_MANAGER_LIST = "/project/project/getList";

  ///客户删除
  static const String CUSTOM_DEL = "/company/custom/delete";

  ///存储改变客户状态
  static const String CUSTOM_SAVE = "/company/custom/save";

  ///获取所有的业态
  static const String GET_BUSINESS_FORMAT_ALL = "/data/project/getCatAll";

  ///获取工作岗位
  static const String GET_JOB_ALL = "/company/job/getAll";

  ///新建、保存项目
  static const String SAVE_COMPANY_PROJECT = "/project/project/save";

  ///获取项目详情
  static const String GET_PROJECT_ONE = "/project/project/getEditDetail";

  ///获取所有的项目内容 - 注意 这个接口不分页
  static const String GET_GENERAL_ALL = "/user/project/getAll";

  ///群体打卡设置
  static const String SETTING_GENERAL_CONFIG = "/project/project/teamClockIn";

  ///获取加班列表
  static const String GET_WORKOVERTIME_LIST = "/kq/overtime/getList";

  ///删除加班记录
  static const String DELETE_WORK_OVERTIME = "/kq/overtime/delete";

  ///获取加班详情记录
  static const String GET_WORK_TIME_ONE = "/kq/overtime/getOne";

  ///编辑、新建加班
  static const String WORK_OVERTIME_SAVE = "/kq/overtime/save";

  ///请假记录
  static const String HOLIDAY_ALL_LIST = "/kq/holiday/getList";

  ///请假详情
  static const String HOLIDAY_ONE = "/kq/holiday/getOne";

  ///请假编辑啊
  static const String HOLIDAY_SAVE = "/kq/holiday/save";

  ///删除请假
  static const String HOLIDAY_DELETE = "/kq/holiday/delete";

  ///考勤日报
  static const String ATTENDANCE_DAY_LIST = "/kq/stat/getDailyList";

  ///考勤月报
  static const String ATTENDANCE_MONTH_LIST = "/kq/stat/getMonthList";

  ///获取所有的班次的列表
  static const String GET_CLASSES_LIST = "/project/classes/getList";

  ///获取排班表的list
  static const String GET_SCHEDULE_ALL_LIST = "/kq/workSchedule/getList";

  ///重置排班表
  static const String GET_SCHEDULE_RESET = "/kq/workSchedule/reset";

  ///导出排班表
  static const String GET_SCHEDULE_EXPORT = "/data/export/workSchedule";

  ///考勤导出
  static const String GET_ATTENDANCE_EXPORT = "/data/export/kqClockInRecord";

  ///考勤原始
  static const String GET_ATTENDANCE_EXPORT_RESULT = "/data/export/kqMonthReport";

  ///修改某日排班班次
  static const String GET_SCHEDULE_UPDATE = "/kq/workSchedule/update";

  ///修改打卡记录
  static const String GET_SCHEDULE_UPDATE_CLOCK = "/kq/daily/updateSegmentClockRecord";

  ///特殊权限列表
  static const String RULES_SPECIAL_LIST = "/user/manageDepartment/getList";

  ///特殊权限删除
  static const String RULES_SPECIAL_DEL = "/user/manageDepartment/delete";

  ///特殊权限编辑
  static const String RULES_SPECIAL_SAVE = "/user/manageDepartment/save";

  ///获取部门列表
  static const String GET_COMPANY_DEPARTMENT_LIST = "/company/department/getList";

  ///删除指定部门
  static const String DELETE_COMPANY_DEPARTMENT = "/company/department/delete";

  ///编辑，新增部门
  static const String SAVE_COMPANY_DEPARTMENT = "/company/department/save";

  ///获取部门负责人列表
  static const String COMPANY_DEPARTMENT_MANAGER_STAFF_LIST = "/company/department/getDirectMemberList";

  ///清空负责人
  static const String CLEAN_COMPANY_DEPARTMENT_MANAGER_STAFF_LIST = "/company/department/cleanUpResponser";

  ///设置负责人
  static const String SET_COMPANY_DEPARTMENT_MANAGER_STAFF_LIST = "/company/department/setResponser";

  ///获取部门详情
  static const String GET_DEPARTMENT_ONE = "/company/department/getOne";

  ///所属部门的列表
  static const String GET_DEPARTMENT_MANAGER_LIST_ONE = "/company/department/getUserManageList";

  ///公司合同的列表
  static const String GET_COMPANY_CONTRACT_LIST = "/company/contractSubject/getList";

  ///获取公司合同详情
  static const String GET_COMPANY_CONTRACT_ONE = "/company/contractSubject/getOne";

  ///删除合同公司
  static const String DEL_COMPANY_CONTRACT_ONE = "/company/contractSubject/delete";

  ///合同撤回公司
  static const String CANCEL_COMPANY_CONTRACT = "/company/contractSubject/cancel";

  ///合同公司撤回认证申请
  static const String CANCEL_COMPANY_CONTRACT_AUTH = "/company/contractSubject/cancelElectronicContractAuth";

  ///合同公司的新建、保存
  static const String SAVE_COMPANY_CONTRACT = "/company/contractSubject/save";

  ///行政组织清除合同公司接口
  static const String CLEAN_COMPANY_CONTRACT = "/company/department/cleanContractSubject";

  ///行政组织关联合同公司接口
  static const String BIND_COMPANY_CONTRACT = "/company/department/bindContractSubject";

  ///获取考勤规则的列表
  static const String GET_ATTENDANCE_MANAGER_LIST = "/project/group/getList";

  ///开票记录
  static const String INSURE_INVOICE_GET_LIST = "/insurance/invoice/getList";

  ///开票详情
  static const String INSURE_INVOICE_GET_ONE = "/insurance/invoice/getOne";

  ///去开票
  static const String INSURE_INVOICE_CREATE = "/insurance/invoice/create";

  ///获取开票金额信息
  static const String INSURE_INVOICE_AMOUNT_INFO = "/insurance/invoice/getAmountInfo";

  ///获取最近的开票信息
  static const String INSURE_INVOICE_AMOUNT_INFO_LAST = "/insurance/invoice/getLatestContractCompany";

  ///获取总部人员详情
  static const String GET_COMPANY_CONTACTA_DETAIL = "/user/archives/getOneHeadOffice";

  ///新增总部人员
  static const String SAVE_COMPANY_CONTACTA = "/user/archives/saveHeadOffice";

  ///删除总部成员
  static const String DELETE_COMPANY_STAFF = "/user/archives/delete";

  ///删除项目
  static const String PROJECT_PROJECT_DELETE = "/project/project/delete";

  ///获取所有的角色权限
  static const String GET_PERMISSIONS = "/company/role/getAll";

  ///获取员工详情
  static const String GET_STAFF_DETAILS = "/user/archives/getOne";

  ///获取审批单子的详情
  static const String GET_DEPART_GET_ONE = "/user/approve/getOne";

  ///员工快速离职
  static const String DEPOART_MEMBERS = "/user/approve/createDimission";

  ///获取合同标签详情
  static const String CREATE_CONTRACT_TAG_ONE_LIST = "/contract/contractLabel/getOne";
  static const String CREATE_CONTRACT = "/contract/contract/create";

  ///如果没有customer_id 注册三方账户
  static const String CREATE_CONTRACT_REGISTER = "/contract/contractCustomer/register";

  /// 纸质合同的操作
  static const String WORK_CONTRACT_SAVE = "/user/paperContract/save";

  /// 已经创建的合同记录
  static const String WORK_CONTRACT_ONE = "/user/paperContract/getOne";

  ///获取考勤规则
  static const String GET_GROUP_ALL = "/project/group/getList";
  static const String SAVE_GROUP = "/project/group/save";
  static const String DEL_GROUP = "/project/group/delete";
  static const String GET_GROUP_DETAIL = "/project/group/getOne";

  ///班次
  static const String GET_CLASSES_ALL = "/project/classes/getList";
  static const String SAVE_CLASSES = "/project/classes/save";
  static const String DEL_CLASSES = "/project/classes/delete";
  static const String GET_CLASSES_DETAIL = "/project/classes/getOne";

  ///地址管理
  static const String GET_ADDRESS_ALL = "/project/workAddress/getList";
  static const String DEL_ADDRESS_ALL = "/project/workAddress/delete";

  ///审批记录 删除
  static const String APPROVE_RECORD_DEL = "/user/approve/delete";
  static const String APPROVE_RECORD_CANCEL = "/user/approve/cancel";

  ///获取下载的链接
  static const String APPROVE_RECORD_EXPORT_DOWNLOAD = "/data/export/excel";

  ///获取指定人的考勤记录
  static const String ATTENDANCE_MY_ONE = "/kq/stat/getUserMonth";

  ///获取当天的考勤详情
  static const String ATTENDANCE_MY_DAYS_ONE = "/kq/daily/getClockInList";

  ///删除修改记录
  static const String DELETE_ATTENDANCE_OPERATELOG = "/kq/daily/deleteUpdateClockRecord";

  ///重置考勤结果
  static const String RESET_ATTENDANCE_OPERATELOG = "/kq/daily/clearUpdateClockRecord";

  ///修改考勤结果记录
  static const String UPDATE_ATTENDANCE_OPERATELOG = "/kq/daily/updateSegmentClockRecord";

  ///身份证识别
  static const String OCR_ID_CARD = "/data/ocr/idCardRecognition";

  ///银行卡识别
  static const String OCR_BANK_CARD = "/data/ocr/bankCardRecognition";

  ///身份证二要素校验
  static const String CHECK_ID_NUMBER_AUTH = "/data/user/checkIdNumber";

  ///草稿身份证校验
  static const String CHECK_ID_NUMBER = "/user/archivesDraft/checkIdNumber";

  ///新建草稿员工
  static const String CREATE_MEMBERS = "/user/archivesDraft/create";

  ///获取草稿详情
  static const String GET_ARCHIVES_DRAFT_DETAIL = "/user/archivesDraft/getOne";

  ///删除已经存在的草稿详情
  static const String DEL_ARCHIVES_DRAFT = "/user/archivesDraft/delete";

  ///更新草稿
  static const String UPDATE_ARCHIVES_DRAFT = "/user/archivesDraft/update";

  ///更新档案
  static const String UPDATE_ARCHIVES = "/user/archives/update";

  ///获取总部项目的uuid
  static const String GET_HEAD_OFFICE = "/project/project/getOneHeadOffice";
}
