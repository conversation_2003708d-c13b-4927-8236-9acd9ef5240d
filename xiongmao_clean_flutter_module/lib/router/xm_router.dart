import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import '../business/common/approve/approve_router.dart';
import '../business/common/attendance/attendance_router.dart';
import '../business/common/balance/balance_main_router.dart';
import '../business/common/base/base_other_router.dart';
import '../business/common/company/company_router.dart';
import '../business/common/contract/contract_ history_router.dart';
import '../business/common/contract/contract_router.dart';
import '../business/common/credit_inquiry/credit_router.dart';
import '../business/common/custom/custom_router.dart';
import '../business/common/identity/identity_router.dart';
import '../business/common/insure/Insure_router.dart';
import '../business/common/jigsaw_puzzle/jigsaw_puzzle_router.dart';
import '../business/common/notfound/not_found_page.dart';
import '../business/common/notice_message/notice_router.dart';
import '../business/common/project/project_router.dart';
import '../business/common/quality_service/region_router.dart';
import '../business/common/risk_monitoring/risk_router.dart';
import '../business/common/roster/roster_filter_router.dart';
import '../business/common/schedule/schedule_router.dart';
import '../business/common/staff/staff_router.dart';
import '../business/common/todo/todo_router.dart';
import '../business/common/web/web_router.dart';
import '../business/common/workpost/work_post_router.dart';

const _notFoundPage = "notFoundPage";

/// 根据页面名称定义对应的页面对象
Map<String, FlutterBoostRouteFactory> _routerMap = {
  _notFoundPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          return const NotFoundPage();
        });
  },
};

void initRouters() {
  _routerMap.addAll(webRouterMap); //webView
  _routerMap.addAll(todoRouterMap); //todo审批流 列表
  _routerMap.addAll(identityRouterMap); //身份核验
  _routerMap.addAll(creditInquiryRouterMap); //信用查询
  _routerMap.addAll(contractRouterMap); //电子合同
  _routerMap.addAll(contractPayRouterMap); //电子合同
  _routerMap.addAll(insureRouterMap); //保险
  _routerMap.addAll(workPostRouterMap); //岗位管理
  _routerMap.addAll(appRoveRouterMap); //审批规则入口
  _routerMap.addAll(contractPaySuccessMap); //电子合同购买成功
  _routerMap.addAll(contractHistoryMap); //电子历史页面
  _routerMap.addAll(contractAutoSignPageMap); //电子历史页面
  _routerMap.addAll(contractEditSealPageMap); //电子历史页面
  _routerMap.addAll(addContractTemplatePageMap); //添加合同模版
  _routerMap.addAll(contractTemplateReviewPageMap); //合同模版预览
  _routerMap.addAll(contractRecordPageMap); //合同记录
  _routerMap.addAll(balanceMainRouterMap); //余额首页
  _routerMap.addAll(rechargeBalanceRouterMap); //余额充值
  _routerMap.addAll(withdrawalBalanceRouterMap); //余额提现界面
  _routerMap.addAll(withdrawalAccountListRouterMap); //余额提现账户列表
  _routerMap.addAll(withdrawalAccountRouterMap); //余额增加提现账号
  _routerMap.addAll(withdrawalAccountBankRouterMap); //余额增加提现账号-银行卡列表
  _routerMap.addAll(withdrawalAccountBindRouterMap); //余额增加提现账号-绑定界面
  _routerMap.addAll(rechargeBalanceWebPageRouterMap); //对公充值的界面
  _routerMap.addAll(regionRouterMap); //清洁区域
  _routerMap.addAll(noticeMessageRouterMap); //系统消息
  _routerMap.addAll(riskRouterMap); //风险监控
  _routerMap.addAll(jigsawPuzzlePageRouterMap); //拼图
  _routerMap.addAll(customRouterMap); //客户管理
  _routerMap.addAll(projectRouterMap); //项目管理
  _routerMap.addAll(scheduleRouterMap); //排班表
  _routerMap.addAll(attendanceRouterMap); //考勤
  _routerMap.addAll(companyAdministrationRouterMap); //公司合同、行政组织管理
  _routerMap.addAll(rosterRouterMap); //花名册相关的界面
  _routerMap.addAll(addCompanyRouterMap); //添加总部成员
  _routerMap.addAll(baseOtherMap); //一些比较调试base的界面
}

/// FlutterBoost 创建路由数据时使用3
Route<dynamic>? routeFactory(RouteSettings settings, String? uniqueId) {
  print("flutter settings name =  ${settings.name}");
  // 跳转到native页面
  if (settings.name != null && settings.name!.startsWith("native_")) {
    print("flutter routeFactory =  return null");
    return null;
  }
  // 否则跳转到flutter页面
  var pageName = settings.name;
  if (pageName == '/') {
    // 初始化，空页面
    // pageName = addStaffPage;

    ///切记：在上线时改为_notFoundPage，只用flutter测试时可用其他页面
    pageName = _notFoundPage;
    // pageName = quickDeparkPage;
  }
  FlutterBoostRouteFactory func = _routerMap[pageName] as FlutterBoostRouteFactory;
  print("flutter routeFactory fuc =  $func");
  return func(settings, uniqueId);
}

/// FlutterBoost 创建路由数据时使用
Widget appBuilder(Widget home) {
  return MaterialApp(
    home: home,
    debugShowCheckedModeBanner: true,

    ///必须加上builder参数，否则showDialog等会出问题
    builder: (_, __) {
      return home;
    },
  );
}
