import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xiongmao_clean_flutter_module/util/qiniu/qiniu_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import '../business/common/quality_service/bean/base_media_entity.dart';
import '../util/common_utils.dart';
import '../util/permission_util.dart';

typedef ImageUrlsCallback = void Function(List<BaseMediaEntity>);
typedef ImageAddCallback = void Function();
typedef ImageDelCallback = void Function(int);

class CustomImageGridView extends StatefulWidget {
  final List<BaseMediaEntity> imageUrls;
  final int maxImageCount;
  final double itemSize;
  final double spacing;
  final Color placeholderColor;
  final bool showAddButton; // 新添加的参数用于控制显示添加图片按钮
  final bool showDelButton; // 新添加的参数用于控制显示删除图片按钮
  final bool addButtonGotoNative; // 添加按钮是否跳转原生
  final bool includeEdge; // 不要边距

  final ImageUrlsCallback? onImageUrlsChanged; //回调参数
  final ImageDelCallback? onDelCustomChanged; //自定义点击回调参数
  final ImageAddCallback? onAddImageChanged; //回调参数

  CustomImageGridView({
    required List<BaseMediaEntity>? imageUrls,
    this.onImageUrlsChanged,
    this.onAddImageChanged,
    this.onDelCustomChanged,
    this.maxImageCount = 9, //最多显示的图片数量
    this.itemSize = 100.0,
    this.spacing = 10.0,
    this.placeholderColor = Colors.grey,
    this.showAddButton = true, // 默认显示添加图片按钮
    this.showDelButton = true, // 默认显示删除图片按钮
    this.addButtonGotoNative = false, // 添加按钮是否跳转原生
    this.includeEdge = true,
  }) : imageUrls = imageUrls ?? <BaseMediaEntity>[];

  List<BaseMediaEntity> getImageUrls() {
    return imageUrls;
  }

  @override
  _CustomImageGridViewState createState() => _CustomImageGridViewState();
}

class _CustomImageGridViewState extends State<CustomImageGridView> {
  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem(
      '拍照',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '相册',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    )
  ];

  void removeImage(int index) {
    setState(() {
      widget.imageUrls.removeAt(index);
      if (widget.onDelCustomChanged != null) {
        widget.onDelCustomChanged!(index);
        return;
      }
      if (widget.onImageUrlsChanged != null) {
        widget.onImageUrlsChanged!(widget.imageUrls);
      }
    });
  }

  void addImage() {
    if (widget.addButtonGotoNative) {
      if (widget.onAddImageChanged != null) {
        widget.onAddImageChanged!();
      }
      return;
    }

    // 展示actionSheet
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return BrnCommonActionSheet(
            actions: actions,
            clickCallBack: (
              int index,
              BrnCommonActionSheetItem actionEle,
            ) async {
              Permission permission;
              String tipMsg;

              if (index == 0) {
                permission = Permission.camera;
                tipMsg = '拍摄照片需要用到相机权限';
              } else {
                permission = await CommonUtils.getRequiredPhotosPermission();
                tipMsg = '选择照片需要用到相册权限';
              }
              permissionUtil.requestPermission(permission, tipMsg: tipMsg, requestSuccessFun: () {
                selectImage(index);
              });
            },
          );
        });
  }

  /// 0 是拍照，1 是相册选
  Future<void> selectImage(int imageSource) async {
    List<Media> pickerPaths = [];

    if (imageSource == 0) {
      // 拍照模式：只允许选择一张图片
      Media? photo = await ImagePickers.openCamera();
      if (photo != null) {
        pickerPaths.add(photo);
      }
    } else {
      // 相册模式：允许多选图片
      pickerPaths = await ImagePickers.pickerPaths(
        galleryMode: GalleryMode.image,
        selectCount: widget.maxImageCount - widget.imageUrls.length, // 剩余可选图片数量
        showGif: false,
        compressSize: 100, // 内置压缩功能，单位为 KB
      );
    }

    if (pickerPaths.isEmpty) {
      return;
    }

    // 串行上传逻辑
    for (int i = 0; i < pickerPaths.length; i++) {
      final photo = pickerPaths[i];
      print('开始处理第 ${i + 1} 张图片 ${photo.path}');

      try {
        // 显示当前图片上传中的提示
        BrnToast.show("第 ${i + 1} 张照片上传中，请稍等", context);

        // 上传图片到七牛云
        await _uploadImageToQiNiu(photo.path ?? '', index: i + 1); // 等待当前图片上传完成
      } catch (e) {
        print("第 ${i + 1} 张图片上传失败: $e");
        BrnToast.show("第 ${i + 1} 张图片上传失败", context);
      }
    }
  }

  /// 单张图片上传到七牛云
  Future<void> _uploadImageToQiNiu(String filePath, {required int index}) async {
    final Completer<void> completer = Completer<void>();

    QiNiuUtils(filePath, statusCallback: (status) {
      print("七牛上传状态---$status");
    }, sendProgressCallback: (progress) {
      print("第 $index 张图片上传进度: $progress%");
    }, successCallback: (keyUrl, hashUrl) {
      print("第 $index 张图片上传成功--$keyUrl");

      setState(() {
        if (widget.imageUrls.length < widget.maxImageCount) {
          BaseMediaEntity data = BaseMediaEntity();
          data.media_url = keyUrl;
          data.media_type = "1";
          widget.imageUrls.add(data); // 更新 imageUrls 列表
          if (widget.onImageUrlsChanged != null) {
            widget.onImageUrlsChanged!(widget.imageUrls);
          }
        }
      });

      completer.complete(); // 标记上传完成
    }, errorCallback: (error) {
      print("第 $index 张图片上传失败--$error");
      completer.completeError(error ?? '第 $index 张图片上传失败'); // 标记上传失败
    }).upload();

    return completer.future; // 等待上传完成
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Padding(
        padding: EdgeInsets.only(left: widget.includeEdge ? 10.0 : 0.0, bottom: 10, right: widget.includeEdge ? 10 : 0, top: 10),
        child: GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            crossAxisSpacing: widget.spacing,
            mainAxisSpacing: widget.spacing,
          ),
          itemCount: widget.showAddButton ? (widget.imageUrls.length < widget.maxImageCount ? widget.imageUrls.length + 1 : widget.imageUrls.length) : widget.imageUrls.length,
          itemBuilder: (context, index) {
            if (index < widget.imageUrls.length) {
              return Stack(
                children: [
                  Positioned.fill(
                    child: InkWell(
                      child: LoadImage(
                        widget.imageUrls[index].media_url ?? "",
                        width: widget.itemSize,
                        height: widget.itemSize,
                        radius: 6,
                      ),
                      onTap: () {
                        var list = widget.imageUrls.map((e) => e.media_url).toList();
                        ImagePickers.previewImages(list, index);
                        // BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_open_preview", "url": "${widget.imageUrls[index].media_url}", "type": "${widget.imageUrls[index].media_type ?? "1"}"});
                      },
                    ),
                  ),
                  Visibility(
                    visible: widget.showDelButton,
                    child: Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () {
                          removeImage(index);
                        },
                        child: const LoadAssetImage(
                          'icon_base_round_close',
                          width: 20,
                          height: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            } else {
              return widget.showAddButton
                  ? GestureDetector(
                      onTap: addImage,
                      child: Container(
                        width: widget.itemSize,
                        height: widget.itemSize,
                        // color: widget.placeholderColor.withOpacity(0.5),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: LoadAssetImage(
                          'common/icon_add_image',
                          width: widget.itemSize,
                          height: widget.itemSize,
                        ),
                      ),
                    )
                  : SizedBox(); // 不显示添加图片按钮时返回空的SizedBox
            }
          },
        ),
      ),
    );
  }
}
