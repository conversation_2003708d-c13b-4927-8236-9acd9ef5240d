import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/template_one_entity.dart';

import '../../business/common/approve/bean/material_cat_child_entity.dart';



ApproveOneEntity $ApproveOneEntityFromJson(Map<String, dynamic> json) {
  final ApproveOneEntity approveOneEntity = ApproveOneEntity();
  final String? applicationTitle = jsonConvert.convert<String>(json['application_title']);
  if (applicationTitle != null) {
    approveOneEntity.applicationTitle = applicationTitle;
  }
  final String? applicationStatus = jsonConvert.convert<String>(json['application_status']);
  if (applicationStatus != null) {
    approveOneEntity.applicationStatus = applicationStatus;
  }
  final String? applicationStatusName = jsonConvert.convert<String>(json['application_status_name']);
  if (applicationStatusName != null) {
    approveOneEntity.applicationStatusName = applicationStatusName;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    approveOneEntity.projectName = projectName;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    approveOneEntity.projectUuid = projectUuid;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    approveOneEntity.remark = remark;
  }
  final List<ApproveOneSummaryList>? summaryList = (json['summary_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveOneSummaryList>(e) as ApproveOneSummaryList).toList();
  if (summaryList != null) {
    approveOneEntity.summaryList = summaryList;
  }
  final List<dynamic>? applicationList = (json['application_list'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (applicationList != null) {
    approveOneEntity.applicationList = applicationList;
  }
  final String? applicationType = jsonConvert.convert<String>(json['application_type']);
  if (applicationType != null) {
    approveOneEntity.applicationType = applicationType;
  }
  final List<TemplateOneFieldList>? fieldList = (json['field_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TemplateOneFieldList>(e) as TemplateOneFieldList).toList();
  if (fieldList != null) {
    approveOneEntity.fieldList = fieldList;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    approveOneEntity.createTime = createTime;
  }
  final ApproveOneMaterialSku? materialSku = jsonConvert.convert<ApproveOneMaterialSku>(json['material_sku']);
  if (materialSku != null) {
    approveOneEntity.materialSku = materialSku;
  }
  final List<ApproveOneSpNodeList>? spNodeList = (json['sp_node_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveOneSpNodeList>(e) as ApproveOneSpNodeList).toList();
  if (spNodeList != null) {
    approveOneEntity.spNodeList = spNodeList;
  }
  return approveOneEntity;
}

Map<String, dynamic> $ApproveOneEntityToJson(ApproveOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['application_title'] = entity.applicationTitle;
  data['application_status'] = entity.applicationStatus;
  data['application_status_name'] = entity.applicationStatusName;
  data['project_name'] = entity.projectName;
  data['project_uuid'] = entity.projectUuid;
  data['remark'] = entity.remark;
  data['summary_list'] = entity.summaryList?.map((v) => v.toJson()).toList();
  data['application_list'] = entity.applicationList;
  data['application_type'] = entity.applicationType;
  data['field_list'] = entity.fieldList?.map((v) => v.toJson()).toList();
  data['create_time'] = entity.createTime;
  data['material_sku'] = entity.materialSku?.toJson();
  data['sp_node_list'] = entity.spNodeList?.map((v) => v.toJson()).toList();
  return data;
}

extension ApproveOneEntityExtension on ApproveOneEntity {
  ApproveOneEntity copyWith({
    String? applicationTitle,
    String? applicationStatus,
    String? applicationStatusName,
    String? projectName,
    String? projectUuid,
    String? remark,
    List<ApproveOneSummaryList>? summaryList,
    List<dynamic>? applicationList,
    String? applicationType,
    List<TemplateOneFieldList>? fieldList,
    String? createTime,
    ApproveOneMaterialSku? materialSku,
    List<ApproveOneSpNodeList>? spNodeList,
  }) {
    return ApproveOneEntity()
      ..applicationTitle = applicationTitle ?? this.applicationTitle
      ..applicationStatus = applicationStatus ?? this.applicationStatus
      ..applicationStatusName = applicationStatusName ?? this.applicationStatusName
      ..projectName = projectName ?? this.projectName
      ..projectUuid = projectUuid ?? this.projectUuid
      ..remark = remark ?? this.remark
      ..summaryList = summaryList ?? this.summaryList
      ..applicationList = applicationList ?? this.applicationList
      ..applicationType = applicationType ?? this.applicationType
      ..fieldList = fieldList ?? this.fieldList
      ..createTime = createTime ?? this.createTime
      ..materialSku = materialSku ?? this.materialSku
      ..spNodeList = spNodeList ?? this.spNodeList;
  }
}

ApproveOneSummaryList $ApproveOneSummaryListFromJson(Map<String, dynamic> json) {
  final ApproveOneSummaryList approveOneSummaryList = ApproveOneSummaryList();
  final String? item = jsonConvert.convert<String>(json['item']);
  if (item != null) {
    approveOneSummaryList.item = item;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    approveOneSummaryList.content = content;
  }
  return approveOneSummaryList;
}

Map<String, dynamic> $ApproveOneSummaryListToJson(ApproveOneSummaryList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['item'] = entity.item;
  data['content'] = entity.content;
  return data;
}

extension ApproveOneSummaryListExtension on ApproveOneSummaryList {
  ApproveOneSummaryList copyWith({
    String? item,
    String? content,
  }) {
    return ApproveOneSummaryList()
      ..item = item ?? this.item
      ..content = content ?? this.content;
  }
}

ApproveOneFieldList $ApproveOneFieldListFromJson(Map<String, dynamic> json) {
  final ApproveOneFieldList approveOneFieldList = ApproveOneFieldList();
  final String? fieldFormName = jsonConvert.convert<String>(json['field_form_name']);
  if (fieldFormName != null) {
    approveOneFieldList.fieldFormName = fieldFormName;
  }
  final String? fieldName = jsonConvert.convert<String>(json['field_name']);
  if (fieldName != null) {
    approveOneFieldList.fieldName = fieldName;
  }
  final String? fieldType = jsonConvert.convert<String>(json['field_type']);
  if (fieldType != null) {
    approveOneFieldList.fieldType = fieldType;
  }
  final List<String>? fieldValue = (json['field_value'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (fieldValue != null) {
    approveOneFieldList.fieldValue = fieldValue;
  }
  final List<dynamic>? tableList = (json['table_list'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (tableList != null) {
    approveOneFieldList.tableList = tableList;
  }
  final List<dynamic>? attachmentList = (json['attachment_list'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (attachmentList != null) {
    approveOneFieldList.attachmentList = attachmentList;
  }
  return approveOneFieldList;
}

Map<String, dynamic> $ApproveOneFieldListToJson(ApproveOneFieldList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['field_form_name'] = entity.fieldFormName;
  data['field_name'] = entity.fieldName;
  data['field_type'] = entity.fieldType;
  data['field_value'] = entity.fieldValue;
  data['table_list'] = entity.tableList;
  data['attachment_list'] = entity.attachmentList;
  return data;
}

extension ApproveOneFieldListExtension on ApproveOneFieldList {
  ApproveOneFieldList copyWith({
    String? fieldFormName,
    String? fieldName,
    String? fieldType,
    List<String>? fieldValue,
    List<dynamic>? tableList,
    List<dynamic>? attachmentList,
  }) {
    return ApproveOneFieldList()
      ..fieldFormName = fieldFormName ?? this.fieldFormName
      ..fieldName = fieldName ?? this.fieldName
      ..fieldType = fieldType ?? this.fieldType
      ..fieldValue = fieldValue ?? this.fieldValue
      ..tableList = tableList ?? this.tableList
      ..attachmentList = attachmentList ?? this.attachmentList;
  }
}

ApproveOneMaterialSku $ApproveOneMaterialSkuFromJson(Map<String, dynamic> json) {
  final ApproveOneMaterialSku approveOneMaterialSku = ApproveOneMaterialSku();
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    approveOneMaterialSku.total = total;
  }
  final List<MaterialCatChildList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<MaterialCatChildList>(e) as MaterialCatChildList).toList();
  if (list != null) {
    approveOneMaterialSku.list = list;
  }
  return approveOneMaterialSku;
}

Map<String, dynamic> $ApproveOneMaterialSkuToJson(ApproveOneMaterialSku entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ApproveOneMaterialSkuExtension on ApproveOneMaterialSku {
  ApproveOneMaterialSku copyWith({
    int? total,
    List<MaterialCatChildList>? list,
  }) {
    return ApproveOneMaterialSku()
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ApproveOneSpNodeList $ApproveOneSpNodeListFromJson(Map<String, dynamic> json) {
  final ApproveOneSpNodeList approveOneSpNodeList = ApproveOneSpNodeList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    approveOneSpNodeList.uuid = uuid;
  }
  final String? nodeName = jsonConvert.convert<String>(json['node_name']);
  if (nodeName != null) {
    approveOneSpNodeList.nodeName = nodeName;
  }
  final String? subNodeName = jsonConvert.convert<String>(json['sub_node_name']);
  if (subNodeName != null) {
    approveOneSpNodeList.subNodeName = subNodeName;
  }
  final int? nodeStatus = jsonConvert.convert<int>(json['node_status']);
  if (nodeStatus != null) {
    approveOneSpNodeList.nodeStatus = nodeStatus;
  }
  final String? nodeStatusName = jsonConvert.convert<String>(json['node_status_name']);
  if (nodeStatusName != null) {
    approveOneSpNodeList.nodeStatusName = nodeStatusName;
  }
  final List<dynamic>? userList = (json['user_list'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (userList != null) {
    approveOneSpNodeList.userList = userList;
  }
  return approveOneSpNodeList;
}

Map<String, dynamic> $ApproveOneSpNodeListToJson(ApproveOneSpNodeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['node_name'] = entity.nodeName;
  data['sub_node_name'] = entity.subNodeName;
  data['node_status'] = entity.nodeStatus;
  data['node_status_name'] = entity.nodeStatusName;
  data['user_list'] = entity.userList;
  return data;
}

extension ApproveOneSpNodeListExtension on ApproveOneSpNodeList {
  ApproveOneSpNodeList copyWith({
    String? uuid,
    String? nodeName,
    String? subNodeName,
    int? nodeStatus,
    String? nodeStatusName,
    List<dynamic>? userList,
  }) {
    return ApproveOneSpNodeList()
      ..uuid = uuid ?? this.uuid
      ..nodeName = nodeName ?? this.nodeName
      ..subNodeName = subNodeName ?? this.subNodeName
      ..nodeStatus = nodeStatus ?? this.nodeStatus
      ..nodeStatusName = nodeStatusName ?? this.nodeStatusName
      ..userList = userList ?? this.userList;
  }
}