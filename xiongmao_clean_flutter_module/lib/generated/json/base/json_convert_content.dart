// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_icon_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_project_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_record_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_role_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_tem_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/custom_template_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/material_cat_child_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/material_cat_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/sku_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/template_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/attendance_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/my_attendance_day_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/my_attendance_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/project_classes_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/account_bank_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/account_code_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_account_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_bill_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_type_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/bank_ocr_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/recharge_pre_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/user_balance_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/bean/department_company_contract_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/bean/department_company_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/bean/department_company_staff_belong_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/add_signature_result_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_all_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_auto_sign_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_fdd_verify_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_electronic_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_my_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_register_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_user_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/credit_inquiry/bean/credit_inquiry_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/custom/bean/custom_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/Insure_history_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/Insure_history_one_new_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/company_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_add_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_one_record_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_policy_invoicing_record_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_policy_invoicing_record_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_recently_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_scheme_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/jigsaw_puzzle/bean/work_circle_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/notice_message/bean/notice_message_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/address_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/attendance_manager_rules_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/attendance_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/business_format_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/classes_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/classes_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/create_project_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/job_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/project_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/project_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/clean_area_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/clean_plan_all_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/clean_plan_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/clean_plan_task_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/plan_task_one_new_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/bean/project_all_new_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/bean/project_archives_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/roster/bean/attendance_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/holiday_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/holiday_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/schedule_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/schedule_export_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/work_overtime_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/work_overtime_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/check_id_number_auth_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/check_id_number_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/city_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_create_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_register_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_staff_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/create_staff_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/ele_status_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/ocr_bank_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/ocr_id_card_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/staff_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/staff_nation_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/user_approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/rules_special_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_rules_config_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/base_uuid_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_meta_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_role_all_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/qi_niu_info_bean_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/role_detail_entity.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => _asT<T>(e, enumConvert: enumConvert)).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) => _asT<T>(e, enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        return convertFuncMap[type]!(value as Map<String, dynamic>) as T;
      } else {
        throw UnimplementedError('$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<ApproveDetailEntity>[] is M) {
      return data.map<ApproveDetailEntity>((Map<String, dynamic> e) => ApproveDetailEntity.fromJson(e)).toList() as M;
    }
    if (<ApproveDetailList>[] is M) {
      return data.map<ApproveDetailList>((Map<String, dynamic> e) => ApproveDetailList.fromJson(e)).toList() as M;
    }
    if (<ApproveDetailListUserList>[] is M) {
      return data.map<ApproveDetailListUserList>((Map<String, dynamic> e) => ApproveDetailListUserList.fromJson(e)).toList() as M;
    }
    if (<ApproveIconEntity>[] is M) {
      return data.map<ApproveIconEntity>((Map<String, dynamic> e) => ApproveIconEntity.fromJson(e)).toList() as M;
    }
    if (<ApproveIconList>[] is M) {
      return data.map<ApproveIconList>((Map<String, dynamic> e) => ApproveIconList.fromJson(e)).toList() as M;
    }
    if (<ApproveOneEntity>[] is M) {
      return data.map<ApproveOneEntity>((Map<String, dynamic> e) => ApproveOneEntity.fromJson(e)).toList() as M;
    }
    if (<ApproveOneSummaryList>[] is M) {
      return data.map<ApproveOneSummaryList>((Map<String, dynamic> e) => ApproveOneSummaryList.fromJson(e)).toList() as M;
    }
    if (<ApproveOneFieldList>[] is M) {
      return data.map<ApproveOneFieldList>((Map<String, dynamic> e) => ApproveOneFieldList.fromJson(e)).toList() as M;
    }
    if (<ApproveOneMaterialSku>[] is M) {
      return data.map<ApproveOneMaterialSku>((Map<String, dynamic> e) => ApproveOneMaterialSku.fromJson(e)).toList() as M;
    }
    if (<ApproveOneSpNodeList>[] is M) {
      return data.map<ApproveOneSpNodeList>((Map<String, dynamic> e) => ApproveOneSpNodeList.fromJson(e)).toList() as M;
    }
    if (<ApproveProjectEntity>[] is M) {
      return data.map<ApproveProjectEntity>((Map<String, dynamic> e) => ApproveProjectEntity.fromJson(e)).toList() as M;
    }
    if (<ApproveProjectList>[] is M) {
      return data.map<ApproveProjectList>((Map<String, dynamic> e) => ApproveProjectList.fromJson(e)).toList() as M;
    }
    if (<ApproveRecordEntity>[] is M) {
      return data.map<ApproveRecordEntity>((Map<String, dynamic> e) => ApproveRecordEntity.fromJson(e)).toList() as M;
    }
    if (<ApproveRecordList>[] is M) {
      return data.map<ApproveRecordList>((Map<String, dynamic> e) => ApproveRecordList.fromJson(e)).toList() as M;
    }
    if (<ApproveRecordListSummaryList>[] is M) {
      return data.map<ApproveRecordListSummaryList>((Map<String, dynamic> e) => ApproveRecordListSummaryList.fromJson(e)).toList() as M;
    }
    if (<ApproveRoleEntity>[] is M) {
      return data.map<ApproveRoleEntity>((Map<String, dynamic> e) => ApproveRoleEntity.fromJson(e)).toList() as M;
    }
    if (<ApproveRoleList>[] is M) {
      return data.map<ApproveRoleList>((Map<String, dynamic> e) => ApproveRoleList.fromJson(e)).toList() as M;
    }
    if (<ApproveTemEntity>[] is M) {
      return data.map<ApproveTemEntity>((Map<String, dynamic> e) => ApproveTemEntity.fromJson(e)).toList() as M;
    }
    if (<ApproveTemList>[] is M) {
      return data.map<ApproveTemList>((Map<String, dynamic> e) => ApproveTemList.fromJson(e)).toList() as M;
    }
    if (<CustomTemplateDataEntity>[] is M) {
      return data.map<CustomTemplateDataEntity>((Map<String, dynamic> e) => CustomTemplateDataEntity.fromJson(e)).toList() as M;
    }
    if (<CustomTemplateDataAttachmentList>[] is M) {
      return data.map<CustomTemplateDataAttachmentList>((Map<String, dynamic> e) => CustomTemplateDataAttachmentList.fromJson(e)).toList() as M;
    }
    if (<MaterialCatChildEntity>[] is M) {
      return data.map<MaterialCatChildEntity>((Map<String, dynamic> e) => MaterialCatChildEntity.fromJson(e)).toList() as M;
    }
    if (<MaterialCatChildList>[] is M) {
      return data.map<MaterialCatChildList>((Map<String, dynamic> e) => MaterialCatChildList.fromJson(e)).toList() as M;
    }
    if (<MaterialCatEntity>[] is M) {
      return data.map<MaterialCatEntity>((Map<String, dynamic> e) => MaterialCatEntity.fromJson(e)).toList() as M;
    }
    if (<MaterialCatList>[] is M) {
      return data.map<MaterialCatList>((Map<String, dynamic> e) => MaterialCatList.fromJson(e)).toList() as M;
    }
    if (<SkuListEntity>[] is M) {
      return data.map<SkuListEntity>((Map<String, dynamic> e) => SkuListEntity.fromJson(e)).toList() as M;
    }
    if (<TemplateOneEntity>[] is M) {
      return data.map<TemplateOneEntity>((Map<String, dynamic> e) => TemplateOneEntity.fromJson(e)).toList() as M;
    }
    if (<TemplateOneManagerUserList>[] is M) {
      return data.map<TemplateOneManagerUserList>((Map<String, dynamic> e) => TemplateOneManagerUserList.fromJson(e)).toList() as M;
    }
    if (<TemplateOneRadiusUserList>[] is M) {
      return data.map<TemplateOneRadiusUserList>((Map<String, dynamic> e) => TemplateOneRadiusUserList.fromJson(e)).toList() as M;
    }
    if (<TemplateOneRadiusRoleList>[] is M) {
      return data.map<TemplateOneRadiusRoleList>((Map<String, dynamic> e) => TemplateOneRadiusRoleList.fromJson(e)).toList() as M;
    }
    if (<TemplateOneRadiusProjectList>[] is M) {
      return data.map<TemplateOneRadiusProjectList>((Map<String, dynamic> e) => TemplateOneRadiusProjectList.fromJson(e)).toList() as M;
    }
    if (<TemplateOneFieldList>[] is M) {
      return data.map<TemplateOneFieldList>((Map<String, dynamic> e) => TemplateOneFieldList.fromJson(e)).toList() as M;
    }
    if (<TemplateOneList>[] is M) {
      return data.map<TemplateOneList>((Map<String, dynamic> e) => TemplateOneList.fromJson(e)).toList() as M;
    }
    if (<AttendanceDataEntity>[] is M) {
      return data.map<AttendanceDataEntity>((Map<String, dynamic> e) => AttendanceDataEntity.fromJson(e)).toList() as M;
    }
    if (<AttendanceDataList>[] is M) {
      return data.map<AttendanceDataList>((Map<String, dynamic> e) => AttendanceDataList.fromJson(e)).toList() as M;
    }
    if (<MyAttendanceDayEntity>[] is M) {
      return data.map<MyAttendanceDayEntity>((Map<String, dynamic> e) => MyAttendanceDayEntity.fromJson(e)).toList() as M;
    }
    if (<MyAttendanceDayList>[] is M) {
      return data.map<MyAttendanceDayList>((Map<String, dynamic> e) => MyAttendanceDayList.fromJson(e)).toList() as M;
    }
    if (<MyAttendanceDayHolidayList>[] is M) {
      return data.map<MyAttendanceDayHolidayList>((Map<String, dynamic> e) => MyAttendanceDayHolidayList.fromJson(e)).toList() as M;
    }
    if (<MyAttendanceDayOvertimeList>[] is M) {
      return data.map<MyAttendanceDayOvertimeList>((Map<String, dynamic> e) => MyAttendanceDayOvertimeList.fromJson(e)).toList() as M;
    }
    if (<MyAttendanceDayOperateLogList>[] is M) {
      return data.map<MyAttendanceDayOperateLogList>((Map<String, dynamic> e) => MyAttendanceDayOperateLogList.fromJson(e)).toList() as M;
    }
    if (<MyAttendanceOneEntity>[] is M) {
      return data.map<MyAttendanceOneEntity>((Map<String, dynamic> e) => MyAttendanceOneEntity.fromJson(e)).toList() as M;
    }
    if (<MyAttendanceOneList>[] is M) {
      return data.map<MyAttendanceOneList>((Map<String, dynamic> e) => MyAttendanceOneList.fromJson(e)).toList() as M;
    }
    if (<ProjectClassesDataEntity>[] is M) {
      return data.map<ProjectClassesDataEntity>((Map<String, dynamic> e) => ProjectClassesDataEntity.fromJson(e)).toList() as M;
    }
    if (<ProjectClassesDataList>[] is M) {
      return data.map<ProjectClassesDataList>((Map<String, dynamic> e) => ProjectClassesDataList.fromJson(e)).toList() as M;
    }
    if (<AccountBankEntity>[] is M) {
      return data.map<AccountBankEntity>((Map<String, dynamic> e) => AccountBankEntity.fromJson(e)).toList() as M;
    }
    if (<AccountBankList>[] is M) {
      return data.map<AccountBankList>((Map<String, dynamic> e) => AccountBankList.fromJson(e)).toList() as M;
    }
    if (<AccountCodeEntity>[] is M) {
      return data.map<AccountCodeEntity>((Map<String, dynamic> e) => AccountCodeEntity.fromJson(e)).toList() as M;
    }
    if (<BalanceAccountEntity>[] is M) {
      return data.map<BalanceAccountEntity>((Map<String, dynamic> e) => BalanceAccountEntity.fromJson(e)).toList() as M;
    }
    if (<BalanceAccountList>[] is M) {
      return data.map<BalanceAccountList>((Map<String, dynamic> e) => BalanceAccountList.fromJson(e)).toList() as M;
    }
    if (<BalanceBillListEntity>[] is M) {
      return data.map<BalanceBillListEntity>((Map<String, dynamic> e) => BalanceBillListEntity.fromJson(e)).toList() as M;
    }
    if (<BalanceBillListList>[] is M) {
      return data.map<BalanceBillListList>((Map<String, dynamic> e) => BalanceBillListList.fromJson(e)).toList() as M;
    }
    if (<BalanceTypeEntity>[] is M) {
      return data.map<BalanceTypeEntity>((Map<String, dynamic> e) => BalanceTypeEntity.fromJson(e)).toList() as M;
    }
    if (<BalanceTypeSrList>[] is M) {
      return data.map<BalanceTypeSrList>((Map<String, dynamic> e) => BalanceTypeSrList.fromJson(e)).toList() as M;
    }
    if (<BalanceTypeZcList>[] is M) {
      return data.map<BalanceTypeZcList>((Map<String, dynamic> e) => BalanceTypeZcList.fromJson(e)).toList() as M;
    }
    if (<BankOcrEntity>[] is M) {
      return data.map<BankOcrEntity>((Map<String, dynamic> e) => BankOcrEntity.fromJson(e)).toList() as M;
    }
    if (<RechargePreEntity>[] is M) {
      return data.map<RechargePreEntity>((Map<String, dynamic> e) => RechargePreEntity.fromJson(e)).toList() as M;
    }
    if (<RechargePreToAccount>[] is M) {
      return data.map<RechargePreToAccount>((Map<String, dynamic> e) => RechargePreToAccount.fromJson(e)).toList() as M;
    }
    if (<RechargePrePrompt>[] is M) {
      return data.map<RechargePrePrompt>((Map<String, dynamic> e) => RechargePrePrompt.fromJson(e)).toList() as M;
    }
    if (<UserBalanceEntity>[] is M) {
      return data.map<UserBalanceEntity>((Map<String, dynamic> e) => UserBalanceEntity.fromJson(e)).toList() as M;
    }
    if (<DepartmentCompanyContractEntity>[] is M) {
      return data.map<DepartmentCompanyContractEntity>((Map<String, dynamic> e) => DepartmentCompanyContractEntity.fromJson(e)).toList() as M;
    }
    if (<DepartmentCompanyContractList>[] is M) {
      return data.map<DepartmentCompanyContractList>((Map<String, dynamic> e) => DepartmentCompanyContractList.fromJson(e)).toList() as M;
    }
    if (<DepartmentCompanyEntity>[] is M) {
      return data.map<DepartmentCompanyEntity>((Map<String, dynamic> e) => DepartmentCompanyEntity.fromJson(e)).toList() as M;
    }
    if (<DepartmentCompanyList>[] is M) {
      return data.map<DepartmentCompanyList>((Map<String, dynamic> e) => DepartmentCompanyList.fromJson(e)).toList() as M;
    }
    if (<DepartmentCompanyListResponseUserList>[] is M) {
      return data.map<DepartmentCompanyListResponseUserList>((Map<String, dynamic> e) => DepartmentCompanyListResponseUserList.fromJson(e)).toList() as M;
    }
    if (<DepartmentCompanyStaffBelongEntity>[] is M) {
      return data.map<DepartmentCompanyStaffBelongEntity>((Map<String, dynamic> e) => DepartmentCompanyStaffBelongEntity.fromJson(e)).toList() as M;
    }
    if (<AddSignatureResultEntity>[] is M) {
      return data.map<AddSignatureResultEntity>((Map<String, dynamic> e) => AddSignatureResultEntity.fromJson(e)).toList() as M;
    }
    if (<ContractAllListEntity>[] is M) {
      return data.map<ContractAllListEntity>((Map<String, dynamic> e) => ContractAllListEntity.fromJson(e)).toList() as M;
    }
    if (<ContractAllListList>[] is M) {
      return data.map<ContractAllListList>((Map<String, dynamic> e) => ContractAllListList.fromJson(e)).toList() as M;
    }
    if (<ContractAllListListSetting>[] is M) {
      return data.map<ContractAllListListSetting>((Map<String, dynamic> e) => ContractAllListListSetting.fromJson(e)).toList() as M;
    }
    if (<ContractAutoSignEntity>[] is M) {
      return data.map<ContractAutoSignEntity>((Map<String, dynamic> e) => ContractAutoSignEntity.fromJson(e)).toList() as M;
    }
    if (<ContractFddVerifyEntity>[] is M) {
      return data.map<ContractFddVerifyEntity>((Map<String, dynamic> e) => ContractFddVerifyEntity.fromJson(e)).toList() as M;
    }
    if (<ContractInfoEntity>[] is M) {
      return data.map<ContractInfoEntity>((Map<String, dynamic> e) => ContractInfoEntity.fromJson(e)).toList() as M;
    }
    if (<ContractInfoList>[] is M) {
      return data.map<ContractInfoList>((Map<String, dynamic> e) => ContractInfoList.fromJson(e)).toList() as M;
    }
    if (<ContractMainElectronicEntity>[] is M) {
      return data.map<ContractMainElectronicEntity>((Map<String, dynamic> e) => ContractMainElectronicEntity.fromJson(e)).toList() as M;
    }
    if (<ContractMainElectronicSignSetting>[] is M) {
      return data.map<ContractMainElectronicSignSetting>((Map<String, dynamic> e) => ContractMainElectronicSignSetting.fromJson(e)).toList() as M;
    }
    if (<ContractMainElectronicSignSettingLabelEnabledStatus>[] is M) {
      return data.map<ContractMainElectronicSignSettingLabelEnabledStatus>((Map<String, dynamic> e) => ContractMainElectronicSignSettingLabelEnabledStatus.fromJson(e)).toList() as M;
    }
    if (<ContractMainElectronicSignSettingDefaultSignLabelCode>[] is M) {
      return data.map<ContractMainElectronicSignSettingDefaultSignLabelCode>((Map<String, dynamic> e) => ContractMainElectronicSignSettingDefaultSignLabelCode.fromJson(e)).toList() as M;
    }
    if (<ContractMainElectronicDepartmentList>[] is M) {
      return data.map<ContractMainElectronicDepartmentList>((Map<String, dynamic> e) => ContractMainElectronicDepartmentList.fromJson(e)).toList() as M;
    }
    if (<ContractMainInfoEntity>[] is M) {
      return data.map<ContractMainInfoEntity>((Map<String, dynamic> e) => ContractMainInfoEntity.fromJson(e)).toList() as M;
    }
    if (<ContractMainInfoCert>[] is M) {
      return data.map<ContractMainInfoCert>((Map<String, dynamic> e) => ContractMainInfoCert.fromJson(e)).toList() as M;
    }
    if (<ContractMainInfoSignSetting>[] is M) {
      return data.map<ContractMainInfoSignSetting>((Map<String, dynamic> e) => ContractMainInfoSignSetting.fromJson(e)).toList() as M;
    }
    if (<ContractMainInfoSignSettingLabelEnabledStatus>[] is M) {
      return data.map<ContractMainInfoSignSettingLabelEnabledStatus>((Map<String, dynamic> e) => ContractMainInfoSignSettingLabelEnabledStatus.fromJson(e)).toList() as M;
    }
    if (<ContractMainInfoSignSettingDefaultSignLabelCode>[] is M) {
      return data.map<ContractMainInfoSignSettingDefaultSignLabelCode>((Map<String, dynamic> e) => ContractMainInfoSignSettingDefaultSignLabelCode.fromJson(e)).toList() as M;
    }
    if (<ContractMyEntity>[] is M) {
      return data.map<ContractMyEntity>((Map<String, dynamic> e) => ContractMyEntity.fromJson(e)).toList() as M;
    }
    if (<ContractMyList>[] is M) {
      return data.map<ContractMyList>((Map<String, dynamic> e) => ContractMyList.fromJson(e)).toList() as M;
    }
    if (<ContractRegisterInfoEntity>[] is M) {
      return data.map<ContractRegisterInfoEntity>((Map<String, dynamic> e) => ContractRegisterInfoEntity.fromJson(e)).toList() as M;
    }
    if (<ContractTagListEntity>[] is M) {
      return data.map<ContractTagListEntity>((Map<String, dynamic> e) => ContractTagListEntity.fromJson(e)).toList() as M;
    }
    if (<ContractTagListList>[] is M) {
      return data.map<ContractTagListList>((Map<String, dynamic> e) => ContractTagListList.fromJson(e)).toList() as M;
    }
    if (<ContractTagOneEntity>[] is M) {
      return data.map<ContractTagOneEntity>((Map<String, dynamic> e) => ContractTagOneEntity.fromJson(e)).toList() as M;
    }
    if (<ContractTagOneSubLabelList>[] is M) {
      return data.map<ContractTagOneSubLabelList>((Map<String, dynamic> e) => ContractTagOneSubLabelList.fromJson(e)).toList() as M;
    }
    if (<ContractUserEntity>[] is M) {
      return data.map<ContractUserEntity>((Map<String, dynamic> e) => ContractUserEntity.fromJson(e)).toList() as M;
    }
    if (<ContractUserList>[] is M) {
      return data.map<ContractUserList>((Map<String, dynamic> e) => ContractUserList.fromJson(e)).toList() as M;
    }
    if (<ContractUserListContractPicList>[] is M) {
      return data.map<ContractUserListContractPicList>((Map<String, dynamic> e) => ContractUserListContractPicList.fromJson(e)).toList() as M;
    }
    if (<CreditInquiryInfoEntity>[] is M) {
      return data.map<CreditInquiryInfoEntity>((Map<String, dynamic> e) => CreditInquiryInfoEntity.fromJson(e)).toList() as M;
    }
    if (<CreditInquiryInfoList>[] is M) {
      return data.map<CreditInquiryInfoList>((Map<String, dynamic> e) => CreditInquiryInfoList.fromJson(e)).toList() as M;
    }
    if (<CustomManagerEntity>[] is M) {
      return data.map<CustomManagerEntity>((Map<String, dynamic> e) => CustomManagerEntity.fromJson(e)).toList() as M;
    }
    if (<CustomManagerList>[] is M) {
      return data.map<CustomManagerList>((Map<String, dynamic> e) => CustomManagerList.fromJson(e)).toList() as M;
    }
    if (<InsureHistoryListEntity>[] is M) {
      return data.map<InsureHistoryListEntity>((Map<String, dynamic> e) => InsureHistoryListEntity.fromJson(e)).toList() as M;
    }
    if (<InsureHistoryListList>[] is M) {
      return data.map<InsureHistoryListList>((Map<String, dynamic> e) => InsureHistoryListList.fromJson(e)).toList() as M;
    }
    if (<InsureHistoryOneNewEntity>[] is M) {
      return data.map<InsureHistoryOneNewEntity>((Map<String, dynamic> e) => InsureHistoryOneNewEntity.fromJson(e)).toList() as M;
    }
    if (<InsureHistoryOneNewInsuredUserList>[] is M) {
      return data.map<InsureHistoryOneNewInsuredUserList>((Map<String, dynamic> e) => InsureHistoryOneNewInsuredUserList.fromJson(e)).toList() as M;
    }
    if (<CompanyOneEntity>[] is M) {
      return data.map<CompanyOneEntity>((Map<String, dynamic> e) => CompanyOneEntity.fromJson(e)).toList() as M;
    }
    if (<InsureAddEntity>[] is M) {
      return data.map<InsureAddEntity>((Map<String, dynamic> e) => InsureAddEntity.fromJson(e)).toList() as M;
    }
    if (<InsureOneRecordEntity>[] is M) {
      return data.map<InsureOneRecordEntity>((Map<String, dynamic> e) => InsureOneRecordEntity.fromJson(e)).toList() as M;
    }
    if (<InsureOneRecordList>[] is M) {
      return data.map<InsureOneRecordList>((Map<String, dynamic> e) => InsureOneRecordList.fromJson(e)).toList() as M;
    }
    if (<InsurePolicyInvoicingRecordEntity>[] is M) {
      return data.map<InsurePolicyInvoicingRecordEntity>((Map<String, dynamic> e) => InsurePolicyInvoicingRecordEntity.fromJson(e)).toList() as M;
    }
    if (<InsurePolicyInvoicingRecordList>[] is M) {
      return data.map<InsurePolicyInvoicingRecordList>((Map<String, dynamic> e) => InsurePolicyInvoicingRecordList.fromJson(e)).toList() as M;
    }
    if (<InsurePolicyInvoicingRecordOneEntity>[] is M) {
      return data.map<InsurePolicyInvoicingRecordOneEntity>((Map<String, dynamic> e) => InsurePolicyInvoicingRecordOneEntity.fromJson(e)).toList() as M;
    }
    if (<InsurePolicyInvoicingRecordOneOrderList>[] is M) {
      return data.map<InsurePolicyInvoicingRecordOneOrderList>((Map<String, dynamic> e) => InsurePolicyInvoicingRecordOneOrderList.fromJson(e)).toList() as M;
    }
    if (<InsureRecentlyOneEntity>[] is M) {
      return data.map<InsureRecentlyOneEntity>((Map<String, dynamic> e) => InsureRecentlyOneEntity.fromJson(e)).toList() as M;
    }
    if (<InsureSchemeDataEntity>[] is M) {
      return data.map<InsureSchemeDataEntity>((Map<String, dynamic> e) => InsureSchemeDataEntity.fromJson(e)).toList() as M;
    }
    if (<InsureSchemeDataList>[] is M) {
      return data.map<InsureSchemeDataList>((Map<String, dynamic> e) => InsureSchemeDataList.fromJson(e)).toList() as M;
    }
    if (<InsureSchemeDataListSkuList>[] is M) {
      return data.map<InsureSchemeDataListSkuList>((Map<String, dynamic> e) => InsureSchemeDataListSkuList.fromJson(e)).toList() as M;
    }
    if (<WorkCircleEntity>[] is M) {
      return data.map<WorkCircleEntity>((Map<String, dynamic> e) => WorkCircleEntity.fromJson(e)).toList() as M;
    }
    if (<WorkCircleList>[] is M) {
      return data.map<WorkCircleList>((Map<String, dynamic> e) => WorkCircleList.fromJson(e)).toList() as M;
    }
    if (<WorkCircleListList>[] is M) {
      return data.map<WorkCircleListList>((Map<String, dynamic> e) => WorkCircleListList.fromJson(e)).toList() as M;
    }
    if (<NoticeMessageEntity>[] is M) {
      return data.map<NoticeMessageEntity>((Map<String, dynamic> e) => NoticeMessageEntity.fromJson(e)).toList() as M;
    }
    if (<NoticeMessageList>[] is M) {
      return data.map<NoticeMessageList>((Map<String, dynamic> e) => NoticeMessageList.fromJson(e)).toList() as M;
    }
    if (<AddressManagerEntity>[] is M) {
      return data.map<AddressManagerEntity>((Map<String, dynamic> e) => AddressManagerEntity.fromJson(e)).toList() as M;
    }
    if (<AddressManagerList>[] is M) {
      return data.map<AddressManagerList>((Map<String, dynamic> e) => AddressManagerList.fromJson(e)).toList() as M;
    }
    if (<AttendanceManagerRulesEntity>[] is M) {
      return data.map<AttendanceManagerRulesEntity>((Map<String, dynamic> e) => AttendanceManagerRulesEntity.fromJson(e)).toList() as M;
    }
    if (<AttendanceManagerRulesList>[] is M) {
      return data.map<AttendanceManagerRulesList>((Map<String, dynamic> e) => AttendanceManagerRulesList.fromJson(e)).toList() as M;
    }
    if (<AttendanceOneEntity>[] is M) {
      return data.map<AttendanceOneEntity>((Map<String, dynamic> e) => AttendanceOneEntity.fromJson(e)).toList() as M;
    }
    if (<AttendanceOneLeaderList>[] is M) {
      return data.map<AttendanceOneLeaderList>((Map<String, dynamic> e) => AttendanceOneLeaderList.fromJson(e)).toList() as M;
    }
    if (<AttendanceOneAddressList>[] is M) {
      return data.map<AttendanceOneAddressList>((Map<String, dynamic> e) => AttendanceOneAddressList.fromJson(e)).toList() as M;
    }
    if (<BusinessFormatEntity>[] is M) {
      return data.map<BusinessFormatEntity>((Map<String, dynamic> e) => BusinessFormatEntity.fromJson(e)).toList() as M;
    }
    if (<BusinessFormatList>[] is M) {
      return data.map<BusinessFormatList>((Map<String, dynamic> e) => BusinessFormatList.fromJson(e)).toList() as M;
    }
    if (<BusinessFormatListChildList>[] is M) {
      return data.map<BusinessFormatListChildList>((Map<String, dynamic> e) => BusinessFormatListChildList.fromJson(e)).toList() as M;
    }
    if (<ClassesManagerEntity>[] is M) {
      return data.map<ClassesManagerEntity>((Map<String, dynamic> e) => ClassesManagerEntity.fromJson(e)).toList() as M;
    }
    if (<ClassesManagerList>[] is M) {
      return data.map<ClassesManagerList>((Map<String, dynamic> e) => ClassesManagerList.fromJson(e)).toList() as M;
    }
    if (<ClassesOneEntity>[] is M) {
      return data.map<ClassesOneEntity>((Map<String, dynamic> e) => ClassesOneEntity.fromJson(e)).toList() as M;
    }
    if (<ClassesOneSegmentList>[] is M) {
      return data.map<ClassesOneSegmentList>((Map<String, dynamic> e) => ClassesOneSegmentList.fromJson(e)).toList() as M;
    }
    if (<CreateProjectEntity>[] is M) {
      return data.map<CreateProjectEntity>((Map<String, dynamic> e) => CreateProjectEntity.fromJson(e)).toList() as M;
    }
    if (<JobManagerEntity>[] is M) {
      return data.map<JobManagerEntity>((Map<String, dynamic> e) => JobManagerEntity.fromJson(e)).toList() as M;
    }
    if (<JobManagerList>[] is M) {
      return data.map<JobManagerList>((Map<String, dynamic> e) => JobManagerList.fromJson(e)).toList() as M;
    }
    if (<ProjectManagerEntity>[] is M) {
      return data.map<ProjectManagerEntity>((Map<String, dynamic> e) => ProjectManagerEntity.fromJson(e)).toList() as M;
    }
    if (<ProjectManagerList>[] is M) {
      return data.map<ProjectManagerList>((Map<String, dynamic> e) => ProjectManagerList.fromJson(e)).toList() as M;
    }
    if (<ProjectOneEntity>[] is M) {
      return data.map<ProjectOneEntity>((Map<String, dynamic> e) => ProjectOneEntity.fromJson(e)).toList() as M;
    }
    if (<CleanAreaEntity>[] is M) {
      return data.map<CleanAreaEntity>((Map<String, dynamic> e) => CleanAreaEntity.fromJson(e)).toList() as M;
    }
    if (<CleanAreaList>[] is M) {
      return data.map<CleanAreaList>((Map<String, dynamic> e) => CleanAreaList.fromJson(e)).toList() as M;
    }
    if (<CleanPlanAllEntity>[] is M) {
      return data.map<CleanPlanAllEntity>((Map<String, dynamic> e) => CleanPlanAllEntity.fromJson(e)).toList() as M;
    }
    if (<CleanPlanAllList>[] is M) {
      return data.map<CleanPlanAllList>((Map<String, dynamic> e) => CleanPlanAllList.fromJson(e)).toList() as M;
    }
    if (<CleanPlanOneEntity>[] is M) {
      return data.map<CleanPlanOneEntity>((Map<String, dynamic> e) => CleanPlanOneEntity.fromJson(e)).toList() as M;
    }
    if (<CleanPlanOneMediaList>[] is M) {
      return data.map<CleanPlanOneMediaList>((Map<String, dynamic> e) => CleanPlanOneMediaList.fromJson(e)).toList() as M;
    }
    if (<CleanPlanTaskEntity>[] is M) {
      return data.map<CleanPlanTaskEntity>((Map<String, dynamic> e) => CleanPlanTaskEntity.fromJson(e)).toList() as M;
    }
    if (<CleanPlanTaskList>[] is M) {
      return data.map<CleanPlanTaskList>((Map<String, dynamic> e) => CleanPlanTaskList.fromJson(e)).toList() as M;
    }
    if (<CleanPlanTaskListMediaList>[] is M) {
      return data.map<CleanPlanTaskListMediaList>((Map<String, dynamic> e) => CleanPlanTaskListMediaList.fromJson(e)).toList() as M;
    }
    if (<CleanPlanTaskListDealMediaList>[] is M) {
      return data.map<CleanPlanTaskListDealMediaList>((Map<String, dynamic> e) => CleanPlanTaskListDealMediaList.fromJson(e)).toList() as M;
    }
    if (<PlanTaskOneNewEntity>[] is M) {
      return data.map<PlanTaskOneNewEntity>((Map<String, dynamic> e) => PlanTaskOneNewEntity.fromJson(e)).toList() as M;
    }
    if (<PlanTaskOneNewMediaList>[] is M) {
      return data.map<PlanTaskOneNewMediaList>((Map<String, dynamic> e) => PlanTaskOneNewMediaList.fromJson(e)).toList() as M;
    }
    if (<PlanTaskOneNewDealMediaList>[] is M) {
      return data.map<PlanTaskOneNewDealMediaList>((Map<String, dynamic> e) => PlanTaskOneNewDealMediaList.fromJson(e)).toList() as M;
    }
    if (<ProjectAllNewEntity>[] is M) {
      return data.map<ProjectAllNewEntity>((Map<String, dynamic> e) => ProjectAllNewEntity.fromJson(e)).toList() as M;
    }
    if (<ProjectAllNewList>[] is M) {
      return data.map<ProjectAllNewList>((Map<String, dynamic> e) => ProjectAllNewList.fromJson(e)).toList() as M;
    }
    if (<ProjectArchivesEntity>[] is M) {
      return data.map<ProjectArchivesEntity>((Map<String, dynamic> e) => ProjectArchivesEntity.fromJson(e)).toList() as M;
    }
    if (<ProjectArchivesList>[] is M) {
      return data.map<ProjectArchivesList>((Map<String, dynamic> e) => ProjectArchivesList.fromJson(e)).toList() as M;
    }
    if (<AttendanceManagerEntity>[] is M) {
      return data.map<AttendanceManagerEntity>((Map<String, dynamic> e) => AttendanceManagerEntity.fromJson(e)).toList() as M;
    }
    if (<AttendanceManagerList>[] is M) {
      return data.map<AttendanceManagerList>((Map<String, dynamic> e) => AttendanceManagerList.fromJson(e)).toList() as M;
    }
    if (<HolidayDataEntity>[] is M) {
      return data.map<HolidayDataEntity>((Map<String, dynamic> e) => HolidayDataEntity.fromJson(e)).toList() as M;
    }
    if (<HolidayDataList>[] is M) {
      return data.map<HolidayDataList>((Map<String, dynamic> e) => HolidayDataList.fromJson(e)).toList() as M;
    }
    if (<HolidayOneEntity>[] is M) {
      return data.map<HolidayOneEntity>((Map<String, dynamic> e) => HolidayOneEntity.fromJson(e)).toList() as M;
    }
    if (<HolidayOneUserList>[] is M) {
      return data.map<HolidayOneUserList>((Map<String, dynamic> e) => HolidayOneUserList.fromJson(e)).toList() as M;
    }
    if (<ScheduleDataEntity>[] is M) {
      return data.map<ScheduleDataEntity>((Map<String, dynamic> e) => ScheduleDataEntity.fromJson(e)).toList() as M;
    }
    if (<ScheduleDataList>[] is M) {
      return data.map<ScheduleDataList>((Map<String, dynamic> e) => ScheduleDataList.fromJson(e)).toList() as M;
    }
    if (<ScheduleDataListScheduleList>[] is M) {
      return data.map<ScheduleDataListScheduleList>((Map<String, dynamic> e) => ScheduleDataListScheduleList.fromJson(e)).toList() as M;
    }
    if (<ScheduleExportDataEntity>[] is M) {
      return data.map<ScheduleExportDataEntity>((Map<String, dynamic> e) => ScheduleExportDataEntity.fromJson(e)).toList() as M;
    }
    if (<WorkOvertimeDataEntity>[] is M) {
      return data.map<WorkOvertimeDataEntity>((Map<String, dynamic> e) => WorkOvertimeDataEntity.fromJson(e)).toList() as M;
    }
    if (<WorkOvertimeDataList>[] is M) {
      return data.map<WorkOvertimeDataList>((Map<String, dynamic> e) => WorkOvertimeDataList.fromJson(e)).toList() as M;
    }
    if (<WorkOvertimeOneEntity>[] is M) {
      return data.map<WorkOvertimeOneEntity>((Map<String, dynamic> e) => WorkOvertimeOneEntity.fromJson(e)).toList() as M;
    }
    if (<WorkOvertimeOneUserList>[] is M) {
      return data.map<WorkOvertimeOneUserList>((Map<String, dynamic> e) => WorkOvertimeOneUserList.fromJson(e)).toList() as M;
    }
    if (<CheckIdNumberAuthEntity>[] is M) {
      return data.map<CheckIdNumberAuthEntity>((Map<String, dynamic> e) => CheckIdNumberAuthEntity.fromJson(e)).toList() as M;
    }
    if (<CheckIdNumberEntity>[] is M) {
      return data.map<CheckIdNumberEntity>((Map<String, dynamic> e) => CheckIdNumberEntity.fromJson(e)).toList() as M;
    }
    if (<CityDataEntity>[] is M) {
      return data.map<CityDataEntity>((Map<String, dynamic> e) => CityDataEntity.fromJson(e)).toList() as M;
    }
    if (<CityDataList>[] is M) {
      return data.map<CityDataList>((Map<String, dynamic> e) => CityDataList.fromJson(e)).toList() as M;
    }
    if (<CityDataListList>[] is M) {
      return data.map<CityDataListList>((Map<String, dynamic> e) => CityDataListList.fromJson(e)).toList() as M;
    }
    if (<CityDataListListList>[] is M) {
      return data.map<CityDataListListList>((Map<String, dynamic> e) => CityDataListListList.fromJson(e)).toList() as M;
    }
    if (<CompanyContactEntity>[] is M) {
      return data.map<CompanyContactEntity>((Map<String, dynamic> e) => CompanyContactEntity.fromJson(e)).toList() as M;
    }
    if (<ContractCreateEntity>[] is M) {
      return data.map<ContractCreateEntity>((Map<String, dynamic> e) => ContractCreateEntity.fromJson(e)).toList() as M;
    }
    if (<ContractRegisterEntity>[] is M) {
      return data.map<ContractRegisterEntity>((Map<String, dynamic> e) => ContractRegisterEntity.fromJson(e)).toList() as M;
    }
    if (<ContractStaffOneEntity>[] is M) {
      return data.map<ContractStaffOneEntity>((Map<String, dynamic> e) => ContractStaffOneEntity.fromJson(e)).toList() as M;
    }
    if (<CreateStaffEntity>[] is M) {
      return data.map<CreateStaffEntity>((Map<String, dynamic> e) => CreateStaffEntity.fromJson(e)).toList() as M;
    }
    if (<EleStatusEntity>[] is M) {
      return data.map<EleStatusEntity>((Map<String, dynamic> e) => EleStatusEntity.fromJson(e)).toList() as M;
    }
    if (<OcrBankInfoEntity>[] is M) {
      return data.map<OcrBankInfoEntity>((Map<String, dynamic> e) => OcrBankInfoEntity.fromJson(e)).toList() as M;
    }
    if (<OcrIdCardEntity>[] is M) {
      return data.map<OcrIdCardEntity>((Map<String, dynamic> e) => OcrIdCardEntity.fromJson(e)).toList() as M;
    }
    if (<PermissionEntity>[] is M) {
      return data.map<PermissionEntity>((Map<String, dynamic> e) => PermissionEntity.fromJson(e)).toList() as M;
    }
    if (<PermissionData>[] is M) {
      return data.map<PermissionData>((Map<String, dynamic> e) => PermissionData.fromJson(e)).toList() as M;
    }
    if (<StaffDetailEntity>[] is M) {
      return data.map<StaffDetailEntity>((Map<String, dynamic> e) => StaffDetailEntity.fromJson(e)).toList() as M;
    }
    if (<StaffDetailIdentity>[] is M) {
      return data.map<StaffDetailIdentity>((Map<String, dynamic> e) => StaffDetailIdentity.fromJson(e)).toList() as M;
    }
    if (<StaffDetailIdentityPicList>[] is M) {
      return data.map<StaffDetailIdentityPicList>((Map<String, dynamic> e) => StaffDetailIdentityPicList.fromJson(e)).toList() as M;
    }
    if (<StaffDetailBank>[] is M) {
      return data.map<StaffDetailBank>((Map<String, dynamic> e) => StaffDetailBank.fromJson(e)).toList() as M;
    }
    if (<StaffDetailContact>[] is M) {
      return data.map<StaffDetailContact>((Map<String, dynamic> e) => StaffDetailContact.fromJson(e)).toList() as M;
    }
    if (<StaffDetailWorkInfo>[] is M) {
      return data.map<StaffDetailWorkInfo>((Map<String, dynamic> e) => StaffDetailWorkInfo.fromJson(e)).toList() as M;
    }
    if (<StaffDetailHealthyPic>[] is M) {
      return data.map<StaffDetailHealthyPic>((Map<String, dynamic> e) => StaffDetailHealthyPic.fromJson(e)).toList() as M;
    }
    if (<StaffDetailHealthyPicPicList>[] is M) {
      return data.map<StaffDetailHealthyPicPicList>((Map<String, dynamic> e) => StaffDetailHealthyPicPicList.fromJson(e)).toList() as M;
    }
    if (<StaffDetailCreditList>[] is M) {
      return data.map<StaffDetailCreditList>((Map<String, dynamic> e) => StaffDetailCreditList.fromJson(e)).toList() as M;
    }
    if (<StaffDetailContract>[] is M) {
      return data.map<StaffDetailContract>((Map<String, dynamic> e) => StaffDetailContract.fromJson(e)).toList() as M;
    }
    if (<StaffDetailLeftContract>[] is M) {
      return data.map<StaffDetailLeftContract>((Map<String, dynamic> e) => StaffDetailLeftContract.fromJson(e)).toList() as M;
    }
    if (<StaffDetailSalaryInsurance>[] is M) {
      return data.map<StaffDetailSalaryInsurance>((Map<String, dynamic> e) => StaffDetailSalaryInsurance.fromJson(e)).toList() as M;
    }
    if (<StaffNationEntity>[] is M) {
      return data.map<StaffNationEntity>((Map<String, dynamic> e) => StaffNationEntity.fromJson(e)).toList() as M;
    }
    if (<StaffNationList>[] is M) {
      return data.map<StaffNationList>((Map<String, dynamic> e) => StaffNationList.fromJson(e)).toList() as M;
    }
    if (<UserApproveDetailEntity>[] is M) {
      return data.map<UserApproveDetailEntity>((Map<String, dynamic> e) => UserApproveDetailEntity.fromJson(e)).toList() as M;
    }
    if (<UserApproveDetailSummaryList>[] is M) {
      return data.map<UserApproveDetailSummaryList>((Map<String, dynamic> e) => UserApproveDetailSummaryList.fromJson(e)).toList() as M;
    }
    if (<UserApproveDetailFieldList>[] is M) {
      return data.map<UserApproveDetailFieldList>((Map<String, dynamic> e) => UserApproveDetailFieldList.fromJson(e)).toList() as M;
    }
    if (<UserApproveDetailMaterialSku>[] is M) {
      return data.map<UserApproveDetailMaterialSku>((Map<String, dynamic> e) => UserApproveDetailMaterialSku.fromJson(e)).toList() as M;
    }
    if (<UserApproveDetailMaterialSkuList>[] is M) {
      return data.map<UserApproveDetailMaterialSkuList>((Map<String, dynamic> e) => UserApproveDetailMaterialSkuList.fromJson(e)).toList() as M;
    }
    if (<UserApproveDetailSpNodeList>[] is M) {
      return data.map<UserApproveDetailSpNodeList>((Map<String, dynamic> e) => UserApproveDetailSpNodeList.fromJson(e)).toList() as M;
    }
    if (<RulesSpecialDataEntity>[] is M) {
      return data.map<RulesSpecialDataEntity>((Map<String, dynamic> e) => RulesSpecialDataEntity.fromJson(e)).toList() as M;
    }
    if (<RulesSpecialDataList>[] is M) {
      return data.map<RulesSpecialDataList>((Map<String, dynamic> e) => RulesSpecialDataList.fromJson(e)).toList() as M;
    }
    if (<RulesSpecialDataListDepartmentList>[] is M) {
      return data.map<RulesSpecialDataListDepartmentList>((Map<String, dynamic> e) => RulesSpecialDataListDepartmentList.fromJson(e)).toList() as M;
    }
    if (<WorkPostManagerEntity>[] is M) {
      return data.map<WorkPostManagerEntity>((Map<String, dynamic> e) => WorkPostManagerEntity.fromJson(e)).toList() as M;
    }
    if (<WorkPostManagerList>[] is M) {
      return data.map<WorkPostManagerList>((Map<String, dynamic> e) => WorkPostManagerList.fromJson(e)).toList() as M;
    }
    if (<WorkRulesConfigEntity>[] is M) {
      return data.map<WorkRulesConfigEntity>((Map<String, dynamic> e) => WorkRulesConfigEntity.fromJson(e)).toList() as M;
    }
    if (<WorkRulesConfigProjectList>[] is M) {
      return data.map<WorkRulesConfigProjectList>((Map<String, dynamic> e) => WorkRulesConfigProjectList.fromJson(e)).toList() as M;
    }
    if (<WorkRulesConfigRoleAccess>[] is M) {
      return data.map<WorkRulesConfigRoleAccess>((Map<String, dynamic> e) => WorkRulesConfigRoleAccess.fromJson(e)).toList() as M;
    }
    if (<BaseUuidEntity>[] is M) {
      return data.map<BaseUuidEntity>((Map<String, dynamic> e) => BaseUuidEntity.fromJson(e)).toList() as M;
    }
    if (<ContractHistoryEntity>[] is M) {
      return data.map<ContractHistoryEntity>((Map<String, dynamic> e) => ContractHistoryEntity.fromJson(e)).toList() as M;
    }
    if (<ContractHistoryList>[] is M) {
      return data.map<ContractHistoryList>((Map<String, dynamic> e) => ContractHistoryList.fromJson(e)).toList() as M;
    }
    if (<GetMetaEntity>[] is M) {
      return data.map<GetMetaEntity>((Map<String, dynamic> e) => GetMetaEntity.fromJson(e)).toList() as M;
    }
    if (<GetMetaDimissionReasonList>[] is M) {
      return data.map<GetMetaDimissionReasonList>((Map<String, dynamic> e) => GetMetaDimissionReasonList.fromJson(e)).toList() as M;
    }
    if (<GetMetaInClassTimeList>[] is M) {
      return data.map<GetMetaInClassTimeList>((Map<String, dynamic> e) => GetMetaInClassTimeList.fromJson(e)).toList() as M;
    }
    if (<GetMetaInClassTimeListBeforeList>[] is M) {
      return data.map<GetMetaInClassTimeListBeforeList>((Map<String, dynamic> e) => GetMetaInClassTimeListBeforeList.fromJson(e)).toList() as M;
    }
    if (<GetMetaInClassTimeListAfterList>[] is M) {
      return data.map<GetMetaInClassTimeListAfterList>((Map<String, dynamic> e) => GetMetaInClassTimeListAfterList.fromJson(e)).toList() as M;
    }
    if (<GetMetaOutClassTimeList>[] is M) {
      return data.map<GetMetaOutClassTimeList>((Map<String, dynamic> e) => GetMetaOutClassTimeList.fromJson(e)).toList() as M;
    }
    if (<GetMetaOutClassTimeListBeforeList>[] is M) {
      return data.map<GetMetaOutClassTimeListBeforeList>((Map<String, dynamic> e) => GetMetaOutClassTimeListBeforeList.fromJson(e)).toList() as M;
    }
    if (<GetMetaOutClassTimeListAfterList>[] is M) {
      return data.map<GetMetaOutClassTimeListAfterList>((Map<String, dynamic> e) => GetMetaOutClassTimeListAfterList.fromJson(e)).toList() as M;
    }
    if (<GetMetaExigencyUserRelationList>[] is M) {
      return data.map<GetMetaExigencyUserRelationList>((Map<String, dynamic> e) => GetMetaExigencyUserRelationList.fromJson(e)).toList() as M;
    }
    if (<GetMetaBankList>[] is M) {
      return data.map<GetMetaBankList>((Map<String, dynamic> e) => GetMetaBankList.fromJson(e)).toList() as M;
    }
    if (<SuperiorsLevelInfo>[] is M) {
      return data.map<SuperiorsLevelInfo>((Map<String, dynamic> e) => SuperiorsLevelInfo.fromJson(e)).toList() as M;
    }
    if (<GetRoleAllEntity>[] is M) {
      return data.map<GetRoleAllEntity>((Map<String, dynamic> e) => GetRoleAllEntity.fromJson(e)).toList() as M;
    }
    if (<GetRoleAllList>[] is M) {
      return data.map<GetRoleAllList>((Map<String, dynamic> e) => GetRoleAllList.fromJson(e)).toList() as M;
    }
    if (<QiNiuInfoBeanEntity>[] is M) {
      return data.map<QiNiuInfoBeanEntity>((Map<String, dynamic> e) => QiNiuInfoBeanEntity.fromJson(e)).toList() as M;
    }
    if (<RoleDetailEntity>[] is M) {
      return data.map<RoleDetailEntity>((Map<String, dynamic> e) => RoleDetailEntity.fromJson(e)).toList() as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (ApproveDetailEntity).toString(): ApproveDetailEntity.fromJson,
    (ApproveDetailList).toString(): ApproveDetailList.fromJson,
    (ApproveDetailListUserList).toString(): ApproveDetailListUserList.fromJson,
    (ApproveIconEntity).toString(): ApproveIconEntity.fromJson,
    (ApproveIconList).toString(): ApproveIconList.fromJson,
    (ApproveOneEntity).toString(): ApproveOneEntity.fromJson,
    (ApproveOneSummaryList).toString(): ApproveOneSummaryList.fromJson,
    (ApproveOneFieldList).toString(): ApproveOneFieldList.fromJson,
    (ApproveOneMaterialSku).toString(): ApproveOneMaterialSku.fromJson,
    (ApproveOneSpNodeList).toString(): ApproveOneSpNodeList.fromJson,
    (ApproveProjectEntity).toString(): ApproveProjectEntity.fromJson,
    (ApproveProjectList).toString(): ApproveProjectList.fromJson,
    (ApproveRecordEntity).toString(): ApproveRecordEntity.fromJson,
    (ApproveRecordList).toString(): ApproveRecordList.fromJson,
    (ApproveRecordListSummaryList).toString(): ApproveRecordListSummaryList.fromJson,
    (ApproveRoleEntity).toString(): ApproveRoleEntity.fromJson,
    (ApproveRoleList).toString(): ApproveRoleList.fromJson,
    (ApproveTemEntity).toString(): ApproveTemEntity.fromJson,
    (ApproveTemList).toString(): ApproveTemList.fromJson,
    (CustomTemplateDataEntity).toString(): CustomTemplateDataEntity.fromJson,
    (CustomTemplateDataAttachmentList).toString(): CustomTemplateDataAttachmentList.fromJson,
    (MaterialCatChildEntity).toString(): MaterialCatChildEntity.fromJson,
    (MaterialCatChildList).toString(): MaterialCatChildList.fromJson,
    (MaterialCatEntity).toString(): MaterialCatEntity.fromJson,
    (MaterialCatList).toString(): MaterialCatList.fromJson,
    (SkuListEntity).toString(): SkuListEntity.fromJson,
    (TemplateOneEntity).toString(): TemplateOneEntity.fromJson,
    (TemplateOneManagerUserList).toString(): TemplateOneManagerUserList.fromJson,
    (TemplateOneRadiusUserList).toString(): TemplateOneRadiusUserList.fromJson,
    (TemplateOneRadiusRoleList).toString(): TemplateOneRadiusRoleList.fromJson,
    (TemplateOneRadiusProjectList).toString(): TemplateOneRadiusProjectList.fromJson,
    (TemplateOneFieldList).toString(): TemplateOneFieldList.fromJson,
    (TemplateOneList).toString(): TemplateOneList.fromJson,
    (AttendanceDataEntity).toString(): AttendanceDataEntity.fromJson,
    (AttendanceDataList).toString(): AttendanceDataList.fromJson,
    (MyAttendanceDayEntity).toString(): MyAttendanceDayEntity.fromJson,
    (MyAttendanceDayList).toString(): MyAttendanceDayList.fromJson,
    (MyAttendanceDayHolidayList).toString(): MyAttendanceDayHolidayList.fromJson,
    (MyAttendanceDayOvertimeList).toString(): MyAttendanceDayOvertimeList.fromJson,
    (MyAttendanceDayOperateLogList).toString(): MyAttendanceDayOperateLogList.fromJson,
    (MyAttendanceOneEntity).toString(): MyAttendanceOneEntity.fromJson,
    (MyAttendanceOneList).toString(): MyAttendanceOneList.fromJson,
    (ProjectClassesDataEntity).toString(): ProjectClassesDataEntity.fromJson,
    (ProjectClassesDataList).toString(): ProjectClassesDataList.fromJson,
    (AccountBankEntity).toString(): AccountBankEntity.fromJson,
    (AccountBankList).toString(): AccountBankList.fromJson,
    (AccountCodeEntity).toString(): AccountCodeEntity.fromJson,
    (BalanceAccountEntity).toString(): BalanceAccountEntity.fromJson,
    (BalanceAccountList).toString(): BalanceAccountList.fromJson,
    (BalanceBillListEntity).toString(): BalanceBillListEntity.fromJson,
    (BalanceBillListList).toString(): BalanceBillListList.fromJson,
    (BalanceTypeEntity).toString(): BalanceTypeEntity.fromJson,
    (BalanceTypeSrList).toString(): BalanceTypeSrList.fromJson,
    (BalanceTypeZcList).toString(): BalanceTypeZcList.fromJson,
    (BankOcrEntity).toString(): BankOcrEntity.fromJson,
    (RechargePreEntity).toString(): RechargePreEntity.fromJson,
    (RechargePreToAccount).toString(): RechargePreToAccount.fromJson,
    (RechargePrePrompt).toString(): RechargePrePrompt.fromJson,
    (UserBalanceEntity).toString(): UserBalanceEntity.fromJson,
    (DepartmentCompanyContractEntity).toString(): DepartmentCompanyContractEntity.fromJson,
    (DepartmentCompanyContractList).toString(): DepartmentCompanyContractList.fromJson,
    (DepartmentCompanyEntity).toString(): DepartmentCompanyEntity.fromJson,
    (DepartmentCompanyList).toString(): DepartmentCompanyList.fromJson,
    (DepartmentCompanyListResponseUserList).toString(): DepartmentCompanyListResponseUserList.fromJson,
    (DepartmentCompanyStaffBelongEntity).toString(): DepartmentCompanyStaffBelongEntity.fromJson,
    (AddSignatureResultEntity).toString(): AddSignatureResultEntity.fromJson,
    (ContractAllListEntity).toString(): ContractAllListEntity.fromJson,
    (ContractAllListList).toString(): ContractAllListList.fromJson,
    (ContractAllListListSetting).toString(): ContractAllListListSetting.fromJson,
    (ContractAutoSignEntity).toString(): ContractAutoSignEntity.fromJson,
    (ContractFddVerifyEntity).toString(): ContractFddVerifyEntity.fromJson,
    (ContractInfoEntity).toString(): ContractInfoEntity.fromJson,
    (ContractInfoList).toString(): ContractInfoList.fromJson,
    (ContractMainElectronicEntity).toString(): ContractMainElectronicEntity.fromJson,
    (ContractMainElectronicSignSetting).toString(): ContractMainElectronicSignSetting.fromJson,
    (ContractMainElectronicSignSettingLabelEnabledStatus).toString(): ContractMainElectronicSignSettingLabelEnabledStatus.fromJson,
    (ContractMainElectronicSignSettingDefaultSignLabelCode).toString(): ContractMainElectronicSignSettingDefaultSignLabelCode.fromJson,
    (ContractMainElectronicDepartmentList).toString(): ContractMainElectronicDepartmentList.fromJson,
    (ContractMainInfoEntity).toString(): ContractMainInfoEntity.fromJson,
    (ContractMainInfoCert).toString(): ContractMainInfoCert.fromJson,
    (ContractMainInfoSignSetting).toString(): ContractMainInfoSignSetting.fromJson,
    (ContractMainInfoSignSettingLabelEnabledStatus).toString(): ContractMainInfoSignSettingLabelEnabledStatus.fromJson,
    (ContractMainInfoSignSettingDefaultSignLabelCode).toString(): ContractMainInfoSignSettingDefaultSignLabelCode.fromJson,
    (ContractMyEntity).toString(): ContractMyEntity.fromJson,
    (ContractMyList).toString(): ContractMyList.fromJson,
    (ContractRegisterInfoEntity).toString(): ContractRegisterInfoEntity.fromJson,
    (ContractTagListEntity).toString(): ContractTagListEntity.fromJson,
    (ContractTagListList).toString(): ContractTagListList.fromJson,
    (ContractTagOneEntity).toString(): ContractTagOneEntity.fromJson,
    (ContractTagOneSubLabelList).toString(): ContractTagOneSubLabelList.fromJson,
    (ContractUserEntity).toString(): ContractUserEntity.fromJson,
    (ContractUserList).toString(): ContractUserList.fromJson,
    (ContractUserListContractPicList).toString(): ContractUserListContractPicList.fromJson,
    (CreditInquiryInfoEntity).toString(): CreditInquiryInfoEntity.fromJson,
    (CreditInquiryInfoList).toString(): CreditInquiryInfoList.fromJson,
    (CustomManagerEntity).toString(): CustomManagerEntity.fromJson,
    (CustomManagerList).toString(): CustomManagerList.fromJson,
    (InsureHistoryListEntity).toString(): InsureHistoryListEntity.fromJson,
    (InsureHistoryListList).toString(): InsureHistoryListList.fromJson,
    (InsureHistoryOneNewEntity).toString(): InsureHistoryOneNewEntity.fromJson,
    (InsureHistoryOneNewInsuredUserList).toString(): InsureHistoryOneNewInsuredUserList.fromJson,
    (CompanyOneEntity).toString(): CompanyOneEntity.fromJson,
    (InsureAddEntity).toString(): InsureAddEntity.fromJson,
    (InsureOneRecordEntity).toString(): InsureOneRecordEntity.fromJson,
    (InsureOneRecordList).toString(): InsureOneRecordList.fromJson,
    (InsurePolicyInvoicingRecordEntity).toString(): InsurePolicyInvoicingRecordEntity.fromJson,
    (InsurePolicyInvoicingRecordList).toString(): InsurePolicyInvoicingRecordList.fromJson,
    (InsurePolicyInvoicingRecordOneEntity).toString(): InsurePolicyInvoicingRecordOneEntity.fromJson,
    (InsurePolicyInvoicingRecordOneOrderList).toString(): InsurePolicyInvoicingRecordOneOrderList.fromJson,
    (InsureRecentlyOneEntity).toString(): InsureRecentlyOneEntity.fromJson,
    (InsureSchemeDataEntity).toString(): InsureSchemeDataEntity.fromJson,
    (InsureSchemeDataList).toString(): InsureSchemeDataList.fromJson,
    (InsureSchemeDataListSkuList).toString(): InsureSchemeDataListSkuList.fromJson,
    (WorkCircleEntity).toString(): WorkCircleEntity.fromJson,
    (WorkCircleList).toString(): WorkCircleList.fromJson,
    (WorkCircleListList).toString(): WorkCircleListList.fromJson,
    (NoticeMessageEntity).toString(): NoticeMessageEntity.fromJson,
    (NoticeMessageList).toString(): NoticeMessageList.fromJson,
    (AddressManagerEntity).toString(): AddressManagerEntity.fromJson,
    (AddressManagerList).toString(): AddressManagerList.fromJson,
    (AttendanceManagerRulesEntity).toString(): AttendanceManagerRulesEntity.fromJson,
    (AttendanceManagerRulesList).toString(): AttendanceManagerRulesList.fromJson,
    (AttendanceOneEntity).toString(): AttendanceOneEntity.fromJson,
    (AttendanceOneLeaderList).toString(): AttendanceOneLeaderList.fromJson,
    (AttendanceOneAddressList).toString(): AttendanceOneAddressList.fromJson,
    (BusinessFormatEntity).toString(): BusinessFormatEntity.fromJson,
    (BusinessFormatList).toString(): BusinessFormatList.fromJson,
    (BusinessFormatListChildList).toString(): BusinessFormatListChildList.fromJson,
    (ClassesManagerEntity).toString(): ClassesManagerEntity.fromJson,
    (ClassesManagerList).toString(): ClassesManagerList.fromJson,
    (ClassesOneEntity).toString(): ClassesOneEntity.fromJson,
    (ClassesOneSegmentList).toString(): ClassesOneSegmentList.fromJson,
    (CreateProjectEntity).toString(): CreateProjectEntity.fromJson,
    (JobManagerEntity).toString(): JobManagerEntity.fromJson,
    (JobManagerList).toString(): JobManagerList.fromJson,
    (ProjectManagerEntity).toString(): ProjectManagerEntity.fromJson,
    (ProjectManagerList).toString(): ProjectManagerList.fromJson,
    (ProjectOneEntity).toString(): ProjectOneEntity.fromJson,
    (CleanAreaEntity).toString(): CleanAreaEntity.fromJson,
    (CleanAreaList).toString(): CleanAreaList.fromJson,
    (CleanPlanAllEntity).toString(): CleanPlanAllEntity.fromJson,
    (CleanPlanAllList).toString(): CleanPlanAllList.fromJson,
    (CleanPlanOneEntity).toString(): CleanPlanOneEntity.fromJson,
    (CleanPlanOneMediaList).toString(): CleanPlanOneMediaList.fromJson,
    (CleanPlanTaskEntity).toString(): CleanPlanTaskEntity.fromJson,
    (CleanPlanTaskList).toString(): CleanPlanTaskList.fromJson,
    (CleanPlanTaskListMediaList).toString(): CleanPlanTaskListMediaList.fromJson,
    (CleanPlanTaskListDealMediaList).toString(): CleanPlanTaskListDealMediaList.fromJson,
    (PlanTaskOneNewEntity).toString(): PlanTaskOneNewEntity.fromJson,
    (PlanTaskOneNewMediaList).toString(): PlanTaskOneNewMediaList.fromJson,
    (PlanTaskOneNewDealMediaList).toString(): PlanTaskOneNewDealMediaList.fromJson,
    (ProjectAllNewEntity).toString(): ProjectAllNewEntity.fromJson,
    (ProjectAllNewList).toString(): ProjectAllNewList.fromJson,
    (ProjectArchivesEntity).toString(): ProjectArchivesEntity.fromJson,
    (ProjectArchivesList).toString(): ProjectArchivesList.fromJson,
    (AttendanceManagerEntity).toString(): AttendanceManagerEntity.fromJson,
    (AttendanceManagerList).toString(): AttendanceManagerList.fromJson,
    (HolidayDataEntity).toString(): HolidayDataEntity.fromJson,
    (HolidayDataList).toString(): HolidayDataList.fromJson,
    (HolidayOneEntity).toString(): HolidayOneEntity.fromJson,
    (HolidayOneUserList).toString(): HolidayOneUserList.fromJson,
    (ScheduleDataEntity).toString(): ScheduleDataEntity.fromJson,
    (ScheduleDataList).toString(): ScheduleDataList.fromJson,
    (ScheduleDataListScheduleList).toString(): ScheduleDataListScheduleList.fromJson,
    (ScheduleExportDataEntity).toString(): ScheduleExportDataEntity.fromJson,
    (WorkOvertimeDataEntity).toString(): WorkOvertimeDataEntity.fromJson,
    (WorkOvertimeDataList).toString(): WorkOvertimeDataList.fromJson,
    (WorkOvertimeOneEntity).toString(): WorkOvertimeOneEntity.fromJson,
    (WorkOvertimeOneUserList).toString(): WorkOvertimeOneUserList.fromJson,
    (CheckIdNumberAuthEntity).toString(): CheckIdNumberAuthEntity.fromJson,
    (CheckIdNumberEntity).toString(): CheckIdNumberEntity.fromJson,
    (CityDataEntity).toString(): CityDataEntity.fromJson,
    (CityDataList).toString(): CityDataList.fromJson,
    (CityDataListList).toString(): CityDataListList.fromJson,
    (CityDataListListList).toString(): CityDataListListList.fromJson,
    (CompanyContactEntity).toString(): CompanyContactEntity.fromJson,
    (ContractCreateEntity).toString(): ContractCreateEntity.fromJson,
    (ContractRegisterEntity).toString(): ContractRegisterEntity.fromJson,
    (ContractStaffOneEntity).toString(): ContractStaffOneEntity.fromJson,
    (CreateStaffEntity).toString(): CreateStaffEntity.fromJson,
    (EleStatusEntity).toString(): EleStatusEntity.fromJson,
    (OcrBankInfoEntity).toString(): OcrBankInfoEntity.fromJson,
    (OcrIdCardEntity).toString(): OcrIdCardEntity.fromJson,
    (PermissionEntity).toString(): PermissionEntity.fromJson,
    (PermissionData).toString(): PermissionData.fromJson,
    (StaffDetailEntity).toString(): StaffDetailEntity.fromJson,
    (StaffDetailIdentity).toString(): StaffDetailIdentity.fromJson,
    (StaffDetailIdentityPicList).toString(): StaffDetailIdentityPicList.fromJson,
    (StaffDetailBank).toString(): StaffDetailBank.fromJson,
    (StaffDetailContact).toString(): StaffDetailContact.fromJson,
    (StaffDetailWorkInfo).toString(): StaffDetailWorkInfo.fromJson,
    (StaffDetailHealthyPic).toString(): StaffDetailHealthyPic.fromJson,
    (StaffDetailHealthyPicPicList).toString(): StaffDetailHealthyPicPicList.fromJson,
    (StaffDetailCreditList).toString(): StaffDetailCreditList.fromJson,
    (StaffDetailContract).toString(): StaffDetailContract.fromJson,
    (StaffDetailLeftContract).toString(): StaffDetailLeftContract.fromJson,
    (StaffDetailSalaryInsurance).toString(): StaffDetailSalaryInsurance.fromJson,
    (StaffNationEntity).toString(): StaffNationEntity.fromJson,
    (StaffNationList).toString(): StaffNationList.fromJson,
    (UserApproveDetailEntity).toString(): UserApproveDetailEntity.fromJson,
    (UserApproveDetailSummaryList).toString(): UserApproveDetailSummaryList.fromJson,
    (UserApproveDetailFieldList).toString(): UserApproveDetailFieldList.fromJson,
    (UserApproveDetailMaterialSku).toString(): UserApproveDetailMaterialSku.fromJson,
    (UserApproveDetailMaterialSkuList).toString(): UserApproveDetailMaterialSkuList.fromJson,
    (UserApproveDetailSpNodeList).toString(): UserApproveDetailSpNodeList.fromJson,
    (RulesSpecialDataEntity).toString(): RulesSpecialDataEntity.fromJson,
    (RulesSpecialDataList).toString(): RulesSpecialDataList.fromJson,
    (RulesSpecialDataListDepartmentList).toString(): RulesSpecialDataListDepartmentList.fromJson,
    (WorkPostManagerEntity).toString(): WorkPostManagerEntity.fromJson,
    (WorkPostManagerList).toString(): WorkPostManagerList.fromJson,
    (WorkRulesConfigEntity).toString(): WorkRulesConfigEntity.fromJson,
    (WorkRulesConfigProjectList).toString(): WorkRulesConfigProjectList.fromJson,
    (WorkRulesConfigRoleAccess).toString(): WorkRulesConfigRoleAccess.fromJson,
    (BaseUuidEntity).toString(): BaseUuidEntity.fromJson,
    (ContractHistoryEntity).toString(): ContractHistoryEntity.fromJson,
    (ContractHistoryList).toString(): ContractHistoryList.fromJson,
    (GetMetaEntity).toString(): GetMetaEntity.fromJson,
    (GetMetaDimissionReasonList).toString(): GetMetaDimissionReasonList.fromJson,
    (GetMetaInClassTimeList).toString(): GetMetaInClassTimeList.fromJson,
    (GetMetaInClassTimeListBeforeList).toString(): GetMetaInClassTimeListBeforeList.fromJson,
    (GetMetaInClassTimeListAfterList).toString(): GetMetaInClassTimeListAfterList.fromJson,
    (GetMetaOutClassTimeList).toString(): GetMetaOutClassTimeList.fromJson,
    (GetMetaOutClassTimeListBeforeList).toString(): GetMetaOutClassTimeListBeforeList.fromJson,
    (GetMetaOutClassTimeListAfterList).toString(): GetMetaOutClassTimeListAfterList.fromJson,
    (GetMetaExigencyUserRelationList).toString(): GetMetaExigencyUserRelationList.fromJson,
    (GetMetaBankList).toString(): GetMetaBankList.fromJson,
    (SuperiorsLevelInfo).toString(): SuperiorsLevelInfo.fromJson,
    (GetRoleAllEntity).toString(): GetRoleAllEntity.fromJson,
    (GetRoleAllList).toString(): GetRoleAllList.fromJson,
    (QiNiuInfoBeanEntity).toString(): QiNiuInfoBeanEntity.fromJson,
    (RoleDetailEntity).toString(): RoleDetailEntity.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}