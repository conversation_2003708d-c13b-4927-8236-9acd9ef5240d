import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/template_one_entity.dart';

import '../../business/common/approve/bean/approve_detail_entity.dart';
import '../../business/common/approve/bean/custom_template_data_entity.dart';



TemplateOneEntity $TemplateOneEntityFromJson(Map<String, dynamic> json) {
  final TemplateOneEntity templateOneEntity = TemplateOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    templateOneEntity.uuid = uuid;
  }
  final String? templateName = jsonConvert.convert<String>(json['template_name']);
  if (templateName != null) {
    templateOneEntity.templateName = templateName;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    templateOneEntity.icon = icon;
  }
  final String? introduction = jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    templateOneEntity.introduction = introduction;
  }
  final String? templateStatus = jsonConvert.convert<String>(json['template_status']);
  if (templateStatus != null) {
    templateOneEntity.templateStatus = templateStatus;
  }
  final String? isSystem = jsonConvert.convert<String>(json['is_system']);
  if (isSystem != null) {
    templateOneEntity.isSystem = isSystem;
  }
  final List<TemplateOneManagerUserList>? managerUserList = (json['manager_user_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TemplateOneManagerUserList>(e) as TemplateOneManagerUserList).toList();
  if (managerUserList != null) {
    templateOneEntity.managerUserList = managerUserList;
  }
  final List<TemplateOneRadiusUserList>? radiusUserList = (json['radius_user_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TemplateOneRadiusUserList>(e) as TemplateOneRadiusUserList).toList();
  if (radiusUserList != null) {
    templateOneEntity.radiusUserList = radiusUserList;
  }
  final List<TemplateOneRadiusRoleList>? radiusRoleList = (json['radius_role_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TemplateOneRadiusRoleList>(e) as TemplateOneRadiusRoleList).toList();
  if (radiusRoleList != null) {
    templateOneEntity.radiusRoleList = radiusRoleList;
  }
  final List<TemplateOneRadiusProjectList>? radiusProjectList = (json['radius_project_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TemplateOneRadiusProjectList>(e) as TemplateOneRadiusProjectList).toList();
  if (radiusProjectList != null) {
    templateOneEntity.radiusProjectList = radiusProjectList;
  }
  final List<TemplateOneFieldList>? fieldList = (json['field_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TemplateOneFieldList>(e) as TemplateOneFieldList).toList();
  if (fieldList != null) {
    templateOneEntity.fieldList = fieldList;
  }
  final List<ApproveDetailList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveDetailList>(e) as ApproveDetailList).toList();
  if (list != null) {
    templateOneEntity.list = list;
  }
  return templateOneEntity;
}

Map<String, dynamic> $TemplateOneEntityToJson(TemplateOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['template_name'] = entity.templateName;
  data['icon'] = entity.icon;
  data['introduction'] = entity.introduction;
  data['template_status'] = entity.templateStatus;
  data['is_system'] = entity.isSystem;
  data['manager_user_list'] = entity.managerUserList?.map((v) => v.toJson()).toList();
  data['radius_user_list'] = entity.radiusUserList?.map((v) => v.toJson()).toList();
  data['radius_role_list'] = entity.radiusRoleList?.map((v) => v.toJson()).toList();
  data['radius_project_list'] = entity.radiusProjectList?.map((v) => v.toJson()).toList();
  data['field_list'] = entity.fieldList?.map((v) => v.toJson()).toList();
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension TemplateOneEntityExtension on TemplateOneEntity {
  TemplateOneEntity copyWith({
    String? uuid,
    String? templateName,
    String? icon,
    String? introduction,
    String? templateStatus,
    String? isSystem,
    List<TemplateOneManagerUserList>? managerUserList,
    List<TemplateOneRadiusUserList>? radiusUserList,
    List<TemplateOneRadiusRoleList>? radiusRoleList,
    List<TemplateOneRadiusProjectList>? radiusProjectList,
    List<TemplateOneFieldList>? fieldList,
    List<ApproveDetailList>? list,
  }) {
    return TemplateOneEntity()
      ..uuid = uuid ?? this.uuid
      ..templateName = templateName ?? this.templateName
      ..icon = icon ?? this.icon
      ..introduction = introduction ?? this.introduction
      ..templateStatus = templateStatus ?? this.templateStatus
      ..isSystem = isSystem ?? this.isSystem
      ..managerUserList = managerUserList ?? this.managerUserList
      ..radiusUserList = radiusUserList ?? this.radiusUserList
      ..radiusRoleList = radiusRoleList ?? this.radiusRoleList
      ..radiusProjectList = radiusProjectList ?? this.radiusProjectList
      ..fieldList = fieldList ?? this.fieldList
      ..list = list ?? this.list;
  }
}

TemplateOneManagerUserList $TemplateOneManagerUserListFromJson(Map<String, dynamic> json) {
  final TemplateOneManagerUserList templateOneManagerUserList = TemplateOneManagerUserList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    templateOneManagerUserList.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    templateOneManagerUserList.userName = userName;
  }
  return templateOneManagerUserList;
}

Map<String, dynamic> $TemplateOneManagerUserListToJson(TemplateOneManagerUserList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  return data;
}

extension TemplateOneManagerUserListExtension on TemplateOneManagerUserList {
  TemplateOneManagerUserList copyWith({
    String? uuid,
    String? userName,
  }) {
    return TemplateOneManagerUserList()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName;
  }
}

TemplateOneRadiusUserList $TemplateOneRadiusUserListFromJson(Map<String, dynamic> json) {
  final TemplateOneRadiusUserList templateOneRadiusUserList = TemplateOneRadiusUserList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    templateOneRadiusUserList.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    templateOneRadiusUserList.userName = userName;
  }
  return templateOneRadiusUserList;
}

Map<String, dynamic> $TemplateOneRadiusUserListToJson(TemplateOneRadiusUserList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  return data;
}

extension TemplateOneRadiusUserListExtension on TemplateOneRadiusUserList {
  TemplateOneRadiusUserList copyWith({
    String? uuid,
    String? userName,
  }) {
    return TemplateOneRadiusUserList()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName;
  }
}

TemplateOneRadiusRoleList $TemplateOneRadiusRoleListFromJson(Map<String, dynamic> json) {
  final TemplateOneRadiusRoleList templateOneRadiusRoleList = TemplateOneRadiusRoleList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    templateOneRadiusRoleList.id = id;
  }
  final String? roleName = jsonConvert.convert<String>(json['role_name']);
  if (roleName != null) {
    templateOneRadiusRoleList.roleName = roleName;
  }
  return templateOneRadiusRoleList;
}

Map<String, dynamic> $TemplateOneRadiusRoleListToJson(TemplateOneRadiusRoleList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['role_name'] = entity.roleName;
  return data;
}

extension TemplateOneRadiusRoleListExtension on TemplateOneRadiusRoleList {
  TemplateOneRadiusRoleList copyWith({
    String? id,
    String? roleName,
  }) {
    return TemplateOneRadiusRoleList()
      ..id = id ?? this.id
      ..roleName = roleName ?? this.roleName;
  }
}

TemplateOneRadiusProjectList $TemplateOneRadiusProjectListFromJson(Map<String, dynamic> json) {
  final TemplateOneRadiusProjectList templateOneRadiusProjectList = TemplateOneRadiusProjectList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    templateOneRadiusProjectList.uuid = uuid;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    templateOneRadiusProjectList.projectShortName = projectShortName;
  }
  return templateOneRadiusProjectList;
}

Map<String, dynamic> $TemplateOneRadiusProjectListToJson(TemplateOneRadiusProjectList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_short_name'] = entity.projectShortName;
  return data;
}

extension TemplateOneRadiusProjectListExtension on TemplateOneRadiusProjectList {
  TemplateOneRadiusProjectList copyWith({
    String? uuid,
    String? projectShortName,
  }) {
    return TemplateOneRadiusProjectList()
      ..uuid = uuid ?? this.uuid
      ..projectShortName = projectShortName ?? this.projectShortName;
  }
}

TemplateOneFieldList $TemplateOneFieldListFromJson(Map<String, dynamic> json) {
  final TemplateOneFieldList templateOneFieldList = TemplateOneFieldList();
  final String? fieldFormName = jsonConvert.convert<String>(json['field_form_name']);
  if (fieldFormName != null) {
    templateOneFieldList.fieldFormName = fieldFormName;
  }
  final String? fieldName = jsonConvert.convert<String>(json['field_name']);
  if (fieldName != null) {
    templateOneFieldList.fieldName = fieldName;
  }
  final String? fieldType = jsonConvert.convert<String>(json['field_type']);
  if (fieldType != null) {
    templateOneFieldList.fieldType = fieldType;
  }
  final String? isMust = jsonConvert.convert<String>(json['is_must']);
  if (isMust != null) {
    templateOneFieldList.isMust = isMust;
  }
  final String? isAbstract = jsonConvert.convert<String>(json['is_abstract']);
  if (isAbstract != null) {
    templateOneFieldList.isAbstract = isAbstract;
  }
  final String? promptText = jsonConvert.convert<String>(json['prompt_text']);
  if (promptText != null) {
    templateOneFieldList.promptText = promptText;
  }
  final String? formatType = jsonConvert.convert<String>(json['format_type']);
  if (formatType != null) {
    templateOneFieldList.formatType = formatType;
  }
  final String? isMustCameraShoot = jsonConvert.convert<String>(json['is_must_camera_shoot']);
  if (isMustCameraShoot != null) {
    templateOneFieldList.isMustCameraShoot = isMustCameraShoot;
  }
  final String? unitName = jsonConvert.convert<String>(json['unit_name']);
  if (unitName != null) {
    templateOneFieldList.unitName = unitName;
  }
  final List<String>? option_list = (json['option_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (option_list != null) {
    templateOneFieldList.option_list = option_list;
  }
  final List<String>? fieldValue = (json['field_value'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (fieldValue != null) {
    templateOneFieldList.fieldValue = fieldValue;
  }
  final List<CustomTemplateDataAttachmentList>? attachmentList = (json['attachment_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CustomTemplateDataAttachmentList>(e) as CustomTemplateDataAttachmentList).toList();
  if (attachmentList != null) {
    templateOneFieldList.attachmentList = attachmentList;
  }
  final List<TemplateOneFieldList>? child = (json['child'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TemplateOneFieldList>(e) as TemplateOneFieldList).toList();
  if (child != null) {
    templateOneFieldList.child = child;
  }
  final List<List<TemplateOneFieldList>>? tableNewList = (json['table_list'] as List<dynamic>?)?.map(
          (e) =>
          (e as List<dynamic>).map(
                  (e) => jsonConvert.convert<TemplateOneFieldList>(e) as TemplateOneFieldList).toList()).toList();
  if (tableNewList != null) {
    templateOneFieldList.tableNewList = tableNewList;
  }
  final List<TemplateOneFieldList>? tableList = (json['tableList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TemplateOneFieldList>(e) as TemplateOneFieldList).toList();
  if (tableList != null) {
    templateOneFieldList.tableList = tableList;
  }
  final TemplateOneFieldList? tableTemplateOriginal = jsonConvert.convert<TemplateOneFieldList>(json['tableTemplateOriginal']);
  if (tableTemplateOriginal != null) {
    templateOneFieldList.tableTemplateOriginal = tableTemplateOriginal;
  }
  return templateOneFieldList;
}

Map<String, dynamic> $TemplateOneFieldListToJson(TemplateOneFieldList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['field_form_name'] = entity.fieldFormName;
  data['field_name'] = entity.fieldName;
  data['field_type'] = entity.fieldType;
  data['is_must'] = entity.isMust;
  data['is_abstract'] = entity.isAbstract;
  data['prompt_text'] = entity.promptText;
  data['format_type'] = entity.formatType;
  data['is_must_camera_shoot'] = entity.isMustCameraShoot;
  data['unit_name'] = entity.unitName;
  data['option_list'] = entity.option_list;
  data['field_value'] = entity.fieldValue;
  data['attachment_list'] = entity.attachmentList.map((v) => v.toJson()).toList();
  data['child'] = entity.child.map((v) => v.toJson()).toList();
  data['table_list'] = entity.tableNewList;
  data['tableList'] = entity.tableList.map((v) => v.toJson()).toList();
  data['tableTemplateOriginal'] = entity.tableTemplateOriginal?.toJson();
  return data;
}

extension TemplateOneFieldListExtension on TemplateOneFieldList {
  TemplateOneFieldList copyWith({
    String? fieldFormName,
    String? fieldName,
    String? fieldType,
    String? isMust,
    String? isAbstract,
    String? promptText,
    String? formatType,
    String? isMustCameraShoot,
    String? unitName,
    List<String>? option_list,
    List<String>? fieldValue,
    List<CustomTemplateDataAttachmentList>? attachmentList,
    List<TemplateOneFieldList>? child,
    List<List<TemplateOneFieldList>>? tableNewList,
    List<TemplateOneFieldList>? tableList,
    TemplateOneFieldList? tableTemplateOriginal,
  }) {
    return TemplateOneFieldList()
      ..fieldFormName = fieldFormName ?? this.fieldFormName
      ..fieldName = fieldName ?? this.fieldName
      ..fieldType = fieldType ?? this.fieldType
      ..isMust = isMust ?? this.isMust
      ..isAbstract = isAbstract ?? this.isAbstract
      ..promptText = promptText ?? this.promptText
      ..formatType = formatType ?? this.formatType
      ..isMustCameraShoot = isMustCameraShoot ?? this.isMustCameraShoot
      ..unitName = unitName ?? this.unitName
      ..option_list = option_list ?? this.option_list
      ..fieldValue = fieldValue ?? this.fieldValue
      ..attachmentList = attachmentList ?? this.attachmentList
      ..child = child ?? this.child
      ..tableNewList = tableNewList ?? this.tableNewList
      ..tableList = tableList ?? this.tableList
      ..tableTemplateOriginal = tableTemplateOriginal ?? this.tableTemplateOriginal;
  }
}

TemplateOneList $TemplateOneListFromJson(Map<String, dynamic> json) {
  final TemplateOneList templateOneList = TemplateOneList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    templateOneList.uuid = uuid;
  }
  final String? nodeName = jsonConvert.convert<String>(json['node_name']);
  if (nodeName != null) {
    templateOneList.nodeName = nodeName;
  }
  final String? subNodeName = jsonConvert.convert<String>(json['sub_node_name']);
  if (subNodeName != null) {
    templateOneList.subNodeName = subNodeName;
  }
  final String? nodeType = jsonConvert.convert<String>(json['node_type']);
  if (nodeType != null) {
    templateOneList.nodeType = nodeType;
  }
  final String? approverType = jsonConvert.convert<String>(json['approver_type']);
  if (approverType != null) {
    templateOneList.approverType = approverType;
  }
  final int? isCosigned = jsonConvert.convert<int>(json['is_cosigned']);
  if (isCosigned != null) {
    templateOneList.isCosigned = isCosigned;
  }
  final String? superiorsLevel = jsonConvert.convert<String>(json['superiors_level']);
  if (superiorsLevel != null) {
    templateOneList.superiorsLevel = superiorsLevel;
  }
  final String? roleId = jsonConvert.convert<String>(json['role_id']);
  if (roleId != null) {
    templateOneList.roleId = roleId;
  }
  final String? superiorsLevelName = jsonConvert.convert<String>(json['superiors_level_name']);
  if (superiorsLevelName != null) {
    templateOneList.superiorsLevelName = superiorsLevelName;
  }
  final String? roleName = jsonConvert.convert<String>(json['role_name']);
  if (roleName != null) {
    templateOneList.roleName = roleName;
  }
  final List<dynamic>? userList = (json['user_list'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (userList != null) {
    templateOneList.userList = userList;
  }
  return templateOneList;
}

Map<String, dynamic> $TemplateOneListToJson(TemplateOneList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['node_name'] = entity.nodeName;
  data['sub_node_name'] = entity.subNodeName;
  data['node_type'] = entity.nodeType;
  data['approver_type'] = entity.approverType;
  data['is_cosigned'] = entity.isCosigned;
  data['superiors_level'] = entity.superiorsLevel;
  data['role_id'] = entity.roleId;
  data['superiors_level_name'] = entity.superiorsLevelName;
  data['role_name'] = entity.roleName;
  data['user_list'] = entity.userList;
  return data;
}

extension TemplateOneListExtension on TemplateOneList {
  TemplateOneList copyWith({
    String? uuid,
    String? nodeName,
    String? subNodeName,
    String? nodeType,
    String? approverType,
    int? isCosigned,
    String? superiorsLevel,
    String? roleId,
    String? superiorsLevelName,
    String? roleName,
    List<dynamic>? userList,
  }) {
    return TemplateOneList()
      ..uuid = uuid ?? this.uuid
      ..nodeName = nodeName ?? this.nodeName
      ..subNodeName = subNodeName ?? this.subNodeName
      ..nodeType = nodeType ?? this.nodeType
      ..approverType = approverType ?? this.approverType
      ..isCosigned = isCosigned ?? this.isCosigned
      ..superiorsLevel = superiorsLevel ?? this.superiorsLevel
      ..roleId = roleId ?? this.roleId
      ..superiorsLevelName = superiorsLevelName ?? this.superiorsLevelName
      ..roleName = roleName ?? this.roleName
      ..userList = userList ?? this.userList;
  }
}