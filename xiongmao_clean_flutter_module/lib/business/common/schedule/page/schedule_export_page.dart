import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/dialog_manager.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_search_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/loading_util.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../attendance/bean/project_classes_data_entity.dart';
import '../../attendance/widget/custom_attendance_classes_view.dart';
import '../../attendance/widget/custom_attendance_holiday_view.dart';
import '../../attendance/widget/custom_attendance_result_view.dart';
import '../bean/day_info.dart';
import '../bean/schedule_data_entity.dart';
import '../controller/schedule_controller.dart';
import '../item/schedule_list_Item.dart';
import '../iview/schedule_iview.dart';
import '../persenter/schedule_persenter.dart';

class ScheduleExportPage extends StatefulWidget {
  String projectUuid;
  String projectName;
  String exportDate;

  ScheduleExportPage({super.key, required this.projectUuid, required this.projectName, required this.exportDate});

  @override
  _ScheduleExportPageState createState() => _ScheduleExportPageState();
}

class _ScheduleExportPageState extends State<ScheduleExportPage> with BasePageMixin<ScheduleExportPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ScheduleExportPage> implements ScheduleIView {
  SchedulePagePresenter? _presenter;

  final ScheduleController _controller = ScheduleController();
  VoidCallback? addProjectListener;

  @override
  void initState() {
    super.initState();

    _controller.projectExportUuid.value = widget.projectUuid;
    _controller.projectExportName.value = widget.projectName;

    if ((httpConfig.role_id == HttpConfig.ROLE_PROJECT_OWNER_ID)) {
      _controller.projectExportUuid.value = httpConfig.project_uuid;
      _controller.projectExportName.value = httpConfig.project_name;
    }


    if (!TextUtil.isEmpty(widget.exportDate)) {
      DateTime date = DateTime.parse(widget.exportDate);
      _controller.projectExportDate.value = '${date.year}年${date.month.toString().padLeft(2, '0')}月';
    }

    ///监听切换项目
    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        if ("全部" == project_name) {
          _controller.projectExportUuid.value = '';
        } else {
          _controller.projectExportUuid.value = project_uuid;
        }
        _controller.projectExportName.value = project_name;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: '排班表导出',
            onBack: () {
              BoostNavigator.instance.pop();
            },
          ),
          body: Column(
            children: [
              BrnTextSelectFormItem(
                isRequire: true,
                value: _controller.projectExportName.value,
                title: "导出项目",
                onTap: () {
                  BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_show_project_dialog", "isChangeAppProject": false, 'isNeedAll': true, 'project_uuid': _controller.projectUuid.value});
                },
              ),
              Gaps.line,
              BrnTextSelectFormItem(
                isRequire: true,
                value: _controller.projectExportDate.value,
                title: "日期范围",
                onTap: () {
                  DateTime now = DateTime.now();
                  DateTime startDate = DateTime(now.year, now.month - 6);
                  DateTime endDate = DateTime(now.year, now.month + 6);

                  if (startDate.month < 1) {
                    startDate = DateTime(startDate.year - 1, startDate.month + 12);
                  }
                  if (endDate.month > 12) {
                    endDate = DateTime(endDate.year + 1, endDate.month - 12);
                  }

                  List<String> monthsInRange = _controller.getMonthsInRange(startDate, endDate);
                  int currentIndex = _controller.getCurrentMonthIndex(monthsInRange);

                  SingleColumnDataPickerView.showSingleColumnDataPicker(context, '选择要导出月份', monthsInRange, currentIndex, (index, selectedText) {
                    print('当前选择的下标：$index');
                    print('当前选择的文本内容：$selectedText');

                    ///导出出来
                    _controller.projectExportDate.value = selectedText;
                  });
                },
              ),
              Container(
                height: 60,
                margin: EdgeInsets.only(top: 10),
                padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                child: BrnBigMainButton(
                  bgColor: Colours.base_primary_green,
                  title: '确定',
                  onTap: () {
                    if (TextUtil.isEmpty(_controller.projectExportDate.value)) {
                      BrnToast.show('请选择导出日期范围', context);
                      return;
                    }
                    _presenter?.exportSchedule(_controller.projectExportDate.value);
                  },
                ),
              )
            ],
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = SchedulePagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void getClasses(ProjectClassesDataEntity dataEntity, String? userName, String? attendanceDate) {}

  @override
  void onReset() {}

  @override
  void export(String downloadUrl, String exportDate) {
    BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "export_download_url", 'url': downloadUrl, 'fileName': '排班表-${_controller.projectExportName.value}-$exportDate'});
  }
}
