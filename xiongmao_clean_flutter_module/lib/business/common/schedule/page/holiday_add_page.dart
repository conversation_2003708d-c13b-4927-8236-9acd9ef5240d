import 'dart:async';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/controller/holiday_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/persenter/holiday_persenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_search_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../generated/role_detail_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/loading_util.dart';
import '../../../../widgets/custom_am_pm_date_picker.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/work_overtime_data_entity.dart';
import '../bean/work_overtime_one_entity.dart';
import '../controller/schedule_controller.dart';
import '../controller/work_overtime_controller.dart';
import '../item/holiday_staff_list_Item.dart';
import '../item/schedule_list_Item.dart';
import '../item/work_overtime_list_Item.dart';
import '../item/work_overtime_staff_list_Item.dart';
import '../iview/holiday_iview.dart';
import '../iview/schedule_iview.dart';
import '../iview/work_overtime_iview.dart';
import '../persenter/schedule_persenter.dart';
import '../persenter/work_overtime_persenter.dart';

class HolidayAddPage extends StatefulWidget {
  String? uuid;

  String? user_uuid;
  String? user_name;
  String? user_avatar;
  String? current_date;
  String? user_project_name;
  String projectUuid;
  String projectName;

  HolidayAddPage({super.key, this.uuid, this.user_uuid, this.user_name, this.user_avatar, this.current_date, this.user_project_name, required this.projectUuid, required this.projectName});

  @override
  _HolidayAddPageState createState() => _HolidayAddPageState();
}

class _HolidayAddPageState extends State<HolidayAddPage> with BasePageMixin<HolidayAddPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<HolidayAddPage> implements HolidayIView {
  HolidayPresenter? _presenter;

  final HolidayController _controller = HolidayController();

  String title = '添加请假记录';

  late TextEditingController remarkEditingController;

  VoidCallback? selectHolidayProjectListener;

  @override
  void initState() {
    super.initState();

    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;

    if (!TextUtil.isEmpty(widget.uuid)) {
      title = '编辑请假记录';
      _presenter?.getWorkHolidayOne(widget.uuid);
    } else {
      if (!TextUtil.isEmpty(widget.user_uuid)) {
        //获取当前用户传进来的时间
        _controller.getCurrentDay(widget.current_date);
      } else {
        //设置当前默认的时间
        _controller.getTheDay();
      }
    }

    if (!TextUtil.isEmpty(widget.user_uuid)) {
      _controller.workHolidayUserList.clear();
      //查看是否从原生过来 直接给客户增加内容
      WorkOvertimeOneUserList data = WorkOvertimeOneUserList();
      data.uuid = widget.user_uuid;
      data.userName = widget.user_name;
      data.avatar = widget.user_avatar;
      data.customStatusName = _controller.workHolidayStyle.value;
      data.projectName = widget.user_project_name;
      _controller.workHolidayUserList.add(data);
    }

    selectHolidayProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        _controller.projectUuid.value = project_uuid;
        _controller.projectName.value = project_name;
      });
    });

    remarkEditingController = TextEditingController(text: _controller.workHolidayRemark.value);
    // 监听Rx变量的变化
    ever<String>(_controller.workHolidayRemark, (String newValue) {
      remarkEditingController.text = newValue;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: title,
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: WillPopScope(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Obx(() => BrnTextSelectFormItem(
                      title: "所属项目",
                      isRequire: true,
                      isEdit: false,
                      value: _controller.projectName.value,
                      onTap: () {
                        // BoostNavigator.instance.push('ProjectManagerPage', arguments: {
                        //   'choose_uuid': _controller.projectUuid.value,
                        //   'isSelected': true,
                        // }).then((value) {
                        //   if (value is ProjectManagerList) {
                        //     _controller.projectUuid.value = value.uuid ?? '';
                        //     _controller.projectName.value = value.projectShortName ?? '';
                        //   }
                        // });
                        // BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_show_project_dialog", "isChangeAppProject": false, 'project_uuid': _controller.projectUuid.value});
                      },
                    )),
                Gaps.line,
                Obx(() => BrnTextSelectFormItem(
                      title: "请假开始",
                      isRequire: true,
                      value: _controller.workHolidayStartShowTime.value,
                      onTap: () {
                        AMPMDatePickerView.showAMPMColumnDataPicker(context, '请假开始时间', _controller.workHolidayStartTime.value, (_controller.workHolidayStartTimeType.value == '1') ? '上午' : '下午', (i, year, month, day) {
                          print('选择的内容 $i - $year - $month - $day');
                          _controller.workHolidayStartTimeType.value = (i == '上午') ? "1" : "2";
                          _controller.workHolidayStartTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)}';
                          _controller.workHolidayStartShowTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)} $i';
                          _controller.getNextDay();
                        });
                      },
                    )),
                Gaps.line,
                Obx(() => BrnTextSelectFormItem(
                      title: "请假结束",
                      value: _controller.workHolidayEndShowTime.value,
                      isRequire: true,
                      onTap: () {
                        AMPMDatePickerView.showAMPMColumnDataPicker(context, '请假结束时间', _controller.workHolidayEndTime.value, (_controller.workHolidayEndTimeType.value == '1') ? '上午' : '下午', (i, year, month, day) {
                          print('选择的内容 $i - $year - $month - $day');
                          _controller.workHolidayEndTimeType.value = (i == '上午') ? "1" : "2";
                          _controller.workHolidayEndTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)}';
                          _controller.workHolidayEndShowTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)} $i';
                        });
                      },
                    )),
                Gaps.line,
                Obx(() => BrnRadioInputFormItem(
                      title: "请假类型",
                      options: const [
                        "事假",
                        "病假",
                      ],
                      value: _controller.workHolidayStyle.value,
                      onChanged: (oldValue, newValue) {
                        _controller.workHolidayStyle.value = newValue ?? '';
                        _controller.updateUserList();
                      },
                    )),
                Gaps.line,
                BrnTextInputFormItem(
                  themeData: BrnFormItemConfig(),
                  controller: remarkEditingController,
                  title: "请假原因",
                  hint: "请输入请假原因",
                  isRequire: false,
                  onChanged: (newValue) {
                    _controller.workHolidayRemark.value = newValue;
                  },
                ),

                ///请假员工
                Container(
                  margin: EdgeInsets.only(top: 10, bottom: 50),
                  color: Colors.white,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 16,
                          right: 16,
                          top: 20,
                          bottom: 20,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                                child: Row(
                              children: [
                                const Padding(
                                  padding: EdgeInsets.only(top: 1),
                                  child: LoadAssetImage(
                                    'common/icon_work_overtime',
                                    width: 20,
                                    height: 20,
                                  ),
                                ),
                                CommonUtils.getSimpleText(
                                  '请假员工',
                                  14,
                                  Colours.base_primary_text_caption,
                                ),
                              ],
                            )),
                            InkWell(
                              child: CommonUtils.getSimpleText('添加员工', 14, Colours.base_primary, fontWeight: FontWeight.bold),
                              onTap: () {
                                BoostNavigator.instance.push('selectPersonnelPage', arguments: {
                                  'title': '选择请假人员',
                                  'channel': 1,
                                  'status': '1',
                                  'multiple': true,
                                  'contract_start_date': _controller.workHolidayStartTime.value,
                                  'contract_end_date': _controller.workHolidayEndTime.value,
                                  'uuids': json.encode(
                                    _controller.workHolidayUserList.value.map((e) => e.uuid).toList() ?? [],
                                  ),
                                }).then((value) {
                                  if (value != null) {
                                    List<ProjectArchivesList> items = value as List<ProjectArchivesList>;
                                    for (ProjectArchivesList item in items) {
                                      bool exists = _controller.workHolidayUserList.any((data) => data.uuid == item.uuid);
                                      if (!exists) {
                                        WorkOvertimeOneUserList data = WorkOvertimeOneUserList();
                                        data.uuid = item.uuid;
                                        data.userName = item.userName;
                                        data.avatar = item.avatar;
                                        data.projectName = item.projectShortName;
                                        data.customStatusName = _controller.workHolidayStyle.value;
                                        _controller.workHolidayUserList.add(data);
                                      }
                                    }
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                      Visibility(
                        visible: _controller.workHolidayUserList.isNotEmpty,
                        child: Gaps.line,
                      ),
                      Obx(() => ListView.builder(
                            shrinkWrap: true,
                            padding: const EdgeInsets.only(top: 10, left: 16, right: 16, bottom: 10),

                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _controller.workHolidayUserList.length,
                            // Example item count
                            itemBuilder: (context, index) {
                              return HolidayStaffListItem(
                                hideDelBut: false,
                                data: _controller.workHolidayUserList[index],
                                onClick: () {
                                  _controller.deleteUserList(_controller.workHolidayUserList[index]);
                                },
                              );
                            },
                          )),
                    ],
                  ),
                )
              ],
            ),
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '提交',
          onTap: () {
            if (TextUtil.isEmpty(_controller.workHolidayStartTime.value)) {
              BrnToast.show('请选择开始时间', context);
              return;
            }
            if (TextUtil.isEmpty(_controller.workHolidayEndTime.value)) {
              BrnToast.show('请选择结束时间', context);
              return;
            }
            // 将时间字符串转换为 DateTime 对象
            DateTime? startDateTime;
            DateTime? endDateTime;

            startDateTime = DateTime.parse(_controller.workHolidayStartTime.value);
            endDateTime = DateTime.parse(_controller.workHolidayEndTime.value);

            // 比较结束时间和开始时间
            if (endDateTime.isBefore(startDateTime)) {
              // 如果结束时间早于开始时间
              BrnToast.show('请假结束时间不能早于开始时间', context);
              return;
            }

            // 处理同一天的情况
            if (startDateTime.year == endDateTime.year && startDateTime.month == endDateTime.month && startDateTime.day == endDateTime.day) {
              // 如果是同一天，比较时间类型
              if (_controller.workHolidayStartTimeType.value == '2' && _controller.workHolidayEndTimeType.value == '1') {
                // 如果开始时间是下午，结束时间是上午
                BrnToast.show('结束时间不能早于开始时间', context);
                return;
              }
            }
            _presenter?.saveWorkHoliday(widget.uuid);
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = HolidayPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void onStatus() {
    BrnToast.show('操作成功', context);
    BoostNavigator.instance.pop();
  }

  @override
  void dispose() {
    super.dispose();
    remarkEditingController.dispose();
    selectHolidayProjectListener?.call();
  }
}
