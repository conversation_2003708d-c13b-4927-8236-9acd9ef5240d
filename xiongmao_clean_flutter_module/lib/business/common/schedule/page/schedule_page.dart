import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/dialog_manager.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_search_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/loading_util.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../attendance/bean/project_classes_data_entity.dart';
import '../../attendance/widget/custom_attendance_classes_view.dart';
import '../../attendance/widget/custom_attendance_holiday_view.dart';
import '../../attendance/widget/custom_attendance_result_view.dart';
import '../bean/day_info.dart';
import '../bean/schedule_data_entity.dart';
import '../controller/schedule_controller.dart';
import '../item/schedule_list_Item.dart';
import '../iview/schedule_iview.dart';
import '../persenter/schedule_persenter.dart';

class SchedulePage extends StatefulWidget {
  String projectUuid;
  String projectName;

  SchedulePage({super.key, required this.projectUuid, required this.projectName});

  @override
  _SchedulePageState createState() => _SchedulePageState();
}

class _SchedulePageState extends State<SchedulePage> with BasePageMixin<SchedulePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<SchedulePage> implements ScheduleIView {
  SchedulePagePresenter? _presenter;

  final ScheduleController _controller = ScheduleController();
  VoidCallback? addProjectListener;

  @override
  void initState() {
    super.initState();

    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;

    if ((httpConfig.role_id == HttpConfig.ROLE_PROJECT_OWNER_ID)) {
      _controller.projectUuid.value = httpConfig.project_uuid;
      _controller.projectName.value = httpConfig.project_name;
    }

    ///获取当前的周范围
    _controller.generateWeeks();

    ///请求数据
    _onRefresh();

    ///监听切换项目
    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        _controller.projectUuid.value = project_uuid;
        _controller.projectName.value = project_name;
        _onRefresh();
      });
    });
  }

  @override
  dispose() {
    super.dispose();
    addProjectListener?.call();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: '排班表',
            centerSubTitle: _controller.projectName.value ?? '',
            onBack: () {
              BoostNavigator.instance.pop();
            },
            actionWidget: Row(
              children: [
                Visibility(
                    visible: (httpConfig.role_id != HttpConfig.ROLE_PROJECT_OWNER_ID),
                    child: InkWell(
                      onTap: () {
                        BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                          "method": "goto_show_project_dialog",
                          "isChangeAppProject": CommonUtils.checkRoleHeadOffice(),
                          'isNeedAll': CommonUtils.checkRoleHeadOffice(),
                          'isHeadOffice': CommonUtils.checkRoleHeadOffice(),
                          'project_uuid': _controller.projectUuid.value,
                        });
                      },
                      child: const Padding(
                        padding: EdgeInsets.only(left: 16),
                        child: LoadAssetImage(
                          "icon_change",
                          width: 20,
                          height: 20,
                        ),
                      ),
                    )),
                InkWell(
                  onTap: () {
                    BoostNavigator.instance.push('ScheduleResetPage', arguments: {
                      'project_uuid': _controller.projectUuid.value,
                      'project_name': _controller.projectName.value,
                      'reset_date': _controller.searchStartDate.value,
                    }).then((value) => _onRefresh());
                  },
                  child: const Padding(
                    padding: EdgeInsets.only(left: 16),
                    child: LoadAssetImage(
                      "base/icon_base_reset",
                      width: 20,
                      height: 20,
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    BoostNavigator.instance.push('ScheduleExportPage', arguments: {
                      'project_uuid': _controller.projectUuid.value,
                      'project_name': _controller.projectName.value,
                      'export_date': _controller.searchStartDate.value,
                    });
                  },
                  child: const Padding(
                    padding: EdgeInsets.only(left: 16, right: 16),
                    child: LoadAssetImage(
                      "base/icon_base_share",
                      width: 20,
                      height: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          body: WillPopScope(
              child: Column(
                children: [
                  Container(
                    color: Colors.white,
                    padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                    child: Row(
                      children: [
                        Expanded(
                            child: CustomSelectedArrowView(
                          dateText: _controller.searchWeek.value,
                          onDateTextPressed: () {
                            SingleColumnDataPickerView.showSingleColumnDataPicker(context, '选周', _controller.weekKeys, _controller.currentWeekIndex, (index, selectedText) {
                              print('当前选择的下标：$index');
                              print('当前选择的文本内容：$selectedText');
                              _controller.currentWeekIndex = index;
                              _controller.currentDate = _controller.parseFormattedDate(_controller.weekKeys[index].split('～')[0]);
                              _controller.generateWeeks();
                              _onRefresh();
                            });
                          },
                          onNextDayPressed: () {
                            _controller.changeWeek(true);
                            _onRefresh();
                          },
                          onPreviousDayPressed: () {
                            _controller.changeWeek(false);
                            _onRefresh();
                          },
                        )),
                        Gaps.hGap10,
                        Expanded(
                          child: CustomSearchView(
                            hint: '输入姓名找人',
                            onTextChanged: (text) {
                              _controller.searchKeyword.value = text;
                              _onRefresh();
                            },
                          ),
                        )
                      ],
                    ),
                  ),

                  ///导航
                  Container(
                    color: Colors.white,
                    child: GridView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.only(left: 10, right: 10, top: 1),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 8,
                        crossAxisSpacing: 10,
                        mainAxisSpacing: 10,
                      ),
                      shrinkWrap: true,
                      itemCount: 8,
                      itemBuilder: (context, index) {
                        return Container(
                          alignment: Alignment.center,
                          color: Colors.white,
                          child: (index == 0)
                              ? CommonUtils.getSimpleText('姓名', 14, Colours.base_primary_text_title)
                              : Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    CommonUtils.getSimpleText('${_controller.currentWeekDaysInfos[index].month}/${_controller.currentWeekDaysInfos[index].day}', 14, Colours.base_primary_text_title, height: 1, fontWeight: FontWeight.bold),
                                    CommonUtils.getSimpleText(_controller.currentWeekDaysInfos[index].week, 12, Colours.base_primary_text_title),
                                  ],
                                ),
                        );
                      },
                    ),
                  ),

                  ///表格数据
                  Expanded(
                      child: MyRefreshListView(
                    itemCount: _controller.list.length,
                    onRefresh: _onRefresh,
                    loadMore: _loadMore,
                    hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
                    itemBuilder: (_, index) {
                      return Container(
                        color: Colors.white,
                        child: Row(
                          children: [
                            Container(
                              width: 54,
                              padding: EdgeInsets.only(top: 4, bottom: 4),
                              child: Column(
                                children: [
                                  CommonUtils.getSimpleText(truncateString(_controller.list[index].userName), 13, Colors.black, softWrap: false, maxLines: 1),
                                  CommonUtils.getSimpleText(truncateString(_controller.list[index].jobName ?? ''), 13, Colors.grey, softWrap: false, maxLines: 1),
                                ],
                              ),
                            ),
                            Expanded(child: _buildContainers(index, _controller.list[index].scheduleList ?? []))
                          ],
                        ),
                      );
                    },
                  )),
                ],
              ),
              onWillPop: () async {
                if (DialogManager.hasOpenDialogs()) {
                  setState(() {
                    _controller.currentClassUUID.value = '';
                    _controller.currentClickUUID.value = '';
                  });
                  DialogManager.dismissAllDialogs(context);
                  return false; // Prevent the app from popping the route
                } else {
                  return true; // Allow the app to pop the route
                }
              }),
        ));
  }

  Widget _buildContainers(int index, List<ScheduleDataListScheduleList> scheduleList) {
    List<Widget> containers = [];
    //从1开始 走，因为第一个里面有无用的数据
    for (int i = 1; i < _controller.currentWeekDaysInfos.length; i++) {
      //拿到当前的对象
      DayInfo days = _controller.currentWeekDaysInfos[i];

      String className = '不在职';
      Widget container = Container();
      //默认直接创建个无的状态，在下面后台返回的数据中再对比日期
      container = Container(
        alignment: Alignment.center,
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colours.base_primary_bg_page,
          borderRadius: BorderRadius.circular(6),
        ),
        child: CommonUtils.getSimpleText(
          className,
          14,
          Colours.text_black1,
          height: 1,
          textAlign: TextAlign.center,
          maxLines: 2,
        ),
      );

      //对比后台的日期，对比到来，就中断当下的循环
      for (ScheduleDataListScheduleList schedule in scheduleList) {
        print('要对比的内容 ${_controller.list[index].userName}-- ${schedule.scheduleDate}  本地存的 ${days.year}-${CommonUtils.formatToTwoDigits(days.month)}-${CommonUtils.formatToTwoDigits(days.day)}');
        if (schedule.scheduleDate == '${days.year}-${CommonUtils.formatToTwoDigits(days.month)}-${CommonUtils.formatToTwoDigits(days.day)}') {
          className = schedule.className ?? '无';
          print('${_controller.list[index].userName} --- 对比成功了  $className');

          ///对比当前的状态
          if (className == '休息') {
            container = InkWell(
              child: Container(
                alignment: Alignment.center,
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: (_controller.currentClickUUID.value == schedule.uuid) ? Colours.base_primary_blue : Colours.shadow_blue1,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: CommonUtils.getSimpleText(
                  className,
                  14,
                  (_controller.currentClickUUID.value == schedule.uuid) ? Colours.white : Colours.shadow_blue2,
                  height: 1,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                ),
              ),
              onTap: () {
                String? projectUuid = schedule.projectUuid;
                _controller.setCurrentProjectUuid(projectUuid ?? '');
                _presenter?.getClassesManager(_controller.list[index].userName, schedule.scheduleDate);
                _controller.currentClassUUID.value = '0';
                setState(() {
                  _controller.currentClickUUID.value = schedule.uuid ?? '';
                });
              },
            );
          } else {
            container = InkWell(
              child: Container(
                alignment: Alignment.center,
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: (_controller.currentClickUUID.value == schedule.uuid) ? Colours.base_primary : Colours.base_primary_select1,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: CommonUtils.getSimpleText(
                  className,
                  14,
                  (_controller.currentClickUUID.value == schedule.uuid) ? Colours.white : Colours.base_primary,
                  height: 1,
                  textAlign: TextAlign.center,
                  softWrap: true,
                  maxLines: 2,
                ),
              ),
              onTap: () {
                String? projectUuid = schedule.projectUuid;
                _controller.setCurrentProjectUuid(projectUuid ?? '');
                _presenter?.getClassesManager(_controller.list[index].userName, schedule.scheduleDate);
                _controller.currentClassUUID.value = schedule.classUuid ?? '';

                setState(() {
                  _controller.currentClickUUID.value = schedule.uuid ?? '';
                });
              },
            );
          }
          break;
        }
      }

      //把上面创建的容器添加到数组中
      containers.add(Expanded(
          child: Padding(
        padding: const EdgeInsets.only(
          right: 8,
        ),
        child: container,
      )));
    }
    return Row(
      children: containers,
    );
  }

  ///处理字数过多的情况
  String truncateString(String? input) {
    if (input == null) {
      return ''; // 处理 null 输入
    }
    return input.length > 3 ? input.substring(0, 3) : input;
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = SchedulePagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void getClasses(ProjectClassesDataEntity dataEntity, String? userName, String? attendanceDate) {
    //往数据中增加一个休息的班次
    DialogManager.setDialogShowing(true);
    ProjectClassesDataList data = ProjectClassesDataList();
    data.uuid = "0";
    data.className = "休息日";
    data.classTime = "该日无需上班";
    dataEntity.list?.insert(0, data);
    //展示log
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return CustomAttendanceClassesDialog(
          dataEntity: dataEntity,
          classUuid: _controller.currentClassUUID.value,
          userName: userName,
          attendanceDate: attendanceDate,
          onClick: (uuid) {
            ///如果不等于空，那么就请求
            if (!TextUtil.isEmpty(uuid)) {
              _presenter?.updateClassType(_controller.currentClickUUID.value, uuid, (uuid == '0') ? '1' : '2');
            }
            setState(() {
              _controller.currentClassUUID.value = '';
              _controller.currentClickUUID.value = '';
            });
            DialogManager.setDialogShowing(false);
            Navigator.of(dialogContext).pop(); // 使用 dialogContext 关闭对话框

            print('当前的下标是那个重制了 ${_controller.currentClickUUID.value}');
            print('是不是休息： ${(TextUtil.isEmpty(uuid) ? '1' : '2')}');
          },
        );
      },
    );
  }

  @override
  void onReset() {
    BrnToast.show('操作成功', context);
    _onRefresh();
  }

  @override
  void export(String downloadUrl, String exportDate) {
    BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "export_download_url", 'url': downloadUrl, 'fileName': '排班表-$exportDate'});
  }
}
