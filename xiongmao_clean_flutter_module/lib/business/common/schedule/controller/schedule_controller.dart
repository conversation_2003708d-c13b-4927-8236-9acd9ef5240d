import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../bean/day_info.dart';
import '../bean/schedule_data_entity.dart';

class ScheduleController extends GetxController {
  var projectName = "全部项目".obs;
  var projectUuid = "".obs;

  var projectExportName = "".obs;
  var projectExportUuid = "".obs;

  var projectExportDate = "".obs;

  ///重置排班表
  var projectResetName = "".obs;
  var projectResetUuid = "".obs;
  var projectResetDate = "".obs;

  late DateTime firstDayOfMonth;
  late DateTime lastDayOfMonth;

  ///*********************************获取当前的导出月份**************************************
  final List<String> months = ["01月", "02月", "03月", "04月", "05月", "06月", "07月", "08月", "09月", "10月", "11月", "12月"];

  String getMonthName(int monthNumber) {
    return months[monthNumber - 1];
  }

  List<String> getMonthsInRange(DateTime start, DateTime end) {
    List<String> result = [];
    for (int year = start.year; year <= end.year; year++) {
      int startMonth = year == start.year ? start.month : 1;
      int endMonth = year == end.year ? end.month : 12;
      for (int month = startMonth; month <= endMonth; month++) {
        result.add('$year年${getMonthName(month)}');
      }
    }
    return result;
  }

  int getCurrentMonthIndex(List<String> monthsInRange) {
    DateTime now = DateTime.now();
    String currentMonthStr = '${now.year}年${getMonthName(now.month)}';
    return monthsInRange.indexOf(currentMonthStr);
  }

  ///选中的月份转换成当月的1号跟最后一天
  void convertToMonthsInRange(String selectedText) {
    // 解析选中的年份和月份
    String yearStr = selectedText.substring(0, 4);
    String monthStr = selectedText.substring(5, 7);
    int year = int.parse(yearStr);
    int month = int.parse(monthStr);

    // 计算第一天和最后一天
    firstDayOfMonth = DateTime(year, month);
    lastDayOfMonth = DateTime(year, month + 1).subtract(const Duration(days: 1));
  }

  ///*********************************获取当前的导出月份**************************************
  ///*********************************获取当前周范围**************************************

  var searchWeek = ''.obs;

  ///接口需要的参数
  var searchStartDate = ''.obs;
  var searchEndDate = ''.obs;
  var searchKeyword = ''.obs;

  ///记录当前点击的是那个
  var currentClickUUID = ''.obs;

  ///点击的班次Class
  var currentClassUUID = ''.obs;

  ///记当前点击了，那个项目uuid 来获取当前的班次
  var currentClickProjectUuid = ''.obs;

  void setCurrentProjectUuid(String uuid) {
    currentClickProjectUuid.value = uuid;
  }

  Map<String, List<DayInfo>> weeks = {};
  List<String> weekKeys = [];
  DateTime currentDate = DateTime.now();
  int currentWeekIndex = 0; // 记录当前周的下标
  List<DayInfo> currentWeekDaysInfos = [];

  void generateWeeks() {
    weeks.clear();
    weekKeys.clear();
    DateTime startDate = currentDate.subtract(const Duration(days: 180)); // 前后各半年
    DateTime endDate = currentDate.add(const Duration(days: 180));

    while (startDate.isBefore(endDate)) {
      DateTime weekStart = startDate.subtract(Duration(days: startDate.weekday - 1));
      DateTime weekEnd = weekStart.add(const Duration(days: 6));
      if (weekEnd.isAfter(endDate)) {
        weekEnd = endDate;
      }
      String key = '${_formatDate(weekStart)}～${_formatDate(weekEnd)}';
      weeks[key] = List.generate(7, (index) {
        DateTime day = weekStart.add(Duration(days: index));
        String weekName = _getWeekName(day.weekday);
        return DayInfo(year: day.year, month: day.month, day: day.day, week: weekName);
      });
      weekKeys.add(key);
      startDate = weekEnd.add(const Duration(days: 1));
    }

    ///重置当前的week值
    searchWeek.value = simpleDateString();
    String currentKey = getCurrentWeekKey();
    // 初始化 currentWeekIndex
    currentWeekIndex = weekKeys.indexOf(currentKey);
    print('当前的周数 $currentKey');

    List<String> parts = currentKey.replaceAll('年', '-').replaceAll('月', '-').replaceAll('日', '').split('～');
    searchStartDate.value = parts[0];
    searchEndDate.value = parts[1];

    ///获取当前周数
    List<DayInfo>? currentWeek = weeks[currentKey];
    currentWeekDaysInfos = currentWeek ?? [];
    currentWeekDaysInfos.insert(0, DayInfo(year: 1, month: 1, day: 1, week: ''));
    print('状态改变来，来获取一波当前周数 - ${currentWeekDaysInfos}');
  }

  String _getWeekName(int weekday) {
    List<String> weeks = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
    return weeks[weekday - 1];
  }

  String _formatDate(DateTime date) {
    return '${date.year}年${date.month.toString().padLeft(2, '0')}月${date.day.toString().padLeft(2, '0')}日';
  }

  String getCurrentWeekKey() {
    for (var entry in weeks.entries) {
      DateTime start = parseFormattedDate(entry.key.split('～')[0]);
      DateTime end = parseFormattedDate(entry.key.split('～')[1]);
      if (currentDate.isAfter(start.subtract(Duration(days: 1))) && currentDate.isBefore(end.add(Duration(days: 1)))) {
        return entry.key;
      }
    }
    return '';
  }

  DateTime parseFormattedDate(String formattedDate) {
    RegExp regExp = RegExp(r'(\d{4})年(\d{2})月(\d{2})日');
    Match? match = regExp.firstMatch(formattedDate);
    if (match != null) {
      int year = int.parse(match.group(1)!);
      int month = int.parse(match.group(2)!);
      int day = int.parse(match.group(3)!);
      return DateTime(year, month, day);
    }
    throw FormatException('Invalid date format');
  }

  void changeWeek(bool next) {
    if (next && currentWeekIndex < weekKeys.length - 1) {
      currentWeekIndex++;
    } else if (!next && currentWeekIndex > 0) {
      currentWeekIndex--;
    }
    currentDate = parseFormattedDate(weekKeys[currentWeekIndex].split('～')[0]);
    generateWeeks();
  }

  String simpleDateString() {
    String currentKey = getCurrentWeekKey();
    RegExp regExp = RegExp(r'(\d{4})年(\d{2})月(\d{2})日～(\d{4})年(\d{2})月(\d{2})日');
    Match? match = regExp.firstMatch(currentKey);

    if (match != null) {
      String startMonth = match.group(2)!;
      String startDay = match.group(3)!;
      String endMonth = match.group(5)!;
      String endDay = match.group(6)!;

      return '$startMonth.$startDay～$endMonth.$endDay';
    }
    return currentKey;
  }

  ///获取当前周的所有数据
  List<DayInfo> getCurrentWeeksDay() {
    String currentKey = getCurrentWeekKey();
    List<DayInfo>? currentWeek = weeks[currentKey];
    currentWeek?.add(DayInfo(year: 1, month: 1, day: 1, week: ''));
    return currentWeek ?? [];
  }

  ///列表现实的东西需要
  var list = <ScheduleDataList>[].obs;

  var totalNumber = "0".obs;

  void initMyList(int total, List<ScheduleDataList> value) {
    totalNumber.value = total.toString();
    list.value = value.toList();
  }

  void updateMyList(List<ScheduleDataList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///*********************************获取当前周范围**************************************
}
