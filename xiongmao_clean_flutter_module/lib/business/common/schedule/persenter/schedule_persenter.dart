import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../net/http_config.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../attendance/bean/project_classes_data_entity.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/schedule_data_entity.dart';
import '../bean/schedule_export_data_entity.dart';
import '../controller/schedule_controller.dart';
import '../iview/schedule_iview.dart';

/// 信用查询的列表
class SchedulePagePresenter extends BasePagePresenter<ScheduleIView> with WidgetsBindingObserver {
  ScheduleController controller;

  SchedulePagePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getScheduleListManager();
  }

  void loadMore() {
    _page++;
    getScheduleListManager();
  }

  ///获取排班表列表
  Future<dynamic> getScheduleListManager() {
    HashMap<String, String> params = HashMap<String, String>();
    params['start_date'] = controller.searchStartDate.value;
    params['end_date'] = controller.searchEndDate.value;
    if (!TextUtil.isEmpty(controller.projectUuid.value)) {
      params['project_uuid'] = controller.projectUuid.value;
    }
    if (!TextUtil.isEmpty(controller.searchKeyword.value)) {
      params['keyword'] = controller.searchKeyword.value;
    }
    params['page'] = '$_page';
    params['size'] = '20';
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ScheduleDataEntity>(Method.get, url: HttpApi.GET_SCHEDULE_ALL_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取当前项目的班次列表
  Future<dynamic> getClassesManager(String? userName, String? attendanceDate) {
    HashMap<String, String> params = HashMap<String, String>();
    params['project_uuid'] = controller.currentClickProjectUuid.value;
    return requestNetwork<ProjectClassesDataEntity>(Method.get, url: HttpApi.GET_CLASSES_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        view.getClasses(data, userName, attendanceDate);
      } else {}
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///重置当前排版表
  Future<dynamic> requestResetSchedule(String start_date, String end_date) {
    HashMap<String, String> params = HashMap<String, String>();
    params['start_date'] = start_date;
    params['end_date'] = end_date;
    if (!TextUtil.isEmpty(controller.projectResetUuid.value)) {
      params['project_uuid'] = controller.projectResetUuid.value;
    }
    if (!TextUtil.isEmpty(controller.searchKeyword.value)) {
      params['keyword'] = controller.searchKeyword.value;
    }

    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<Object>(Method.get, url: HttpApi.GET_SCHEDULE_RESET, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.onReset();
    });
  }

  ///排班表导出
  Future<dynamic> exportSchedule(String date) {
    HashMap<String, String> params = HashMap<String, String>();
    params['search_month'] = date.replaceAll("年", "-").replaceAll("月", "");
    if (!TextUtil.isEmpty(controller.projectExportUuid.value)) {
      params['project_uuid'] = controller.projectExportUuid.value;
    }
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ScheduleExportDataEntity>(Method.post, url: HttpApi.GET_SCHEDULE_EXPORT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.export(data.downloadUrl ?? '', date);
      }
    });
  }

  ///修改某个排班下的班次类型
  Future<dynamic> updateClassType(String uuid, String? class_uuid, String is_rest) {
    HashMap<String, String> params = HashMap<String, String>();
    params['uuid'] = uuid; //排班UUID
    if (!TextUtil.isEmpty(class_uuid)) {
      //班次UUID 非休息必传
      params['class_uuid'] = "$class_uuid";
    }
    params['is_rest'] = is_rest;
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_SCHEDULE_UPDATE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.onReset();
    });
  }
}
