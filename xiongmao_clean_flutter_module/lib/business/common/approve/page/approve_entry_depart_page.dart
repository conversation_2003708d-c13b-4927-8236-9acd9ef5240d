import 'dart:collection';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_entry_depart_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_main_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/presenter/approve_entry_depart_presenter.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/base_uuid_entity.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../iview/approve_entry_depart_iview.dart';

/**
 * 这里是入职/离职的审批规则
 *
 */

class AppEntryDepartMainPage extends StatefulWidget {
  final String uuid;
  final List<ApproveDetailList> list;

  AppEntryDepartMainPage({Key? key, this.uuid = "", required this.list}) : super(key: key);

  @override
  _AppEntryDepartPageState createState() => _AppEntryDepartPageState();
}

class _AppEntryDepartPageState extends State<AppEntryDepartMainPage> with BasePageMixin<AppEntryDepartMainPage, PowerPresenter<dynamic>> implements ApproveEntryDepartPageView {
  late ApproveEntryDepartPresenter _presenter;
  final ApproveEntryDepartController _controller = ApproveEntryDepartController();
  bool isDragging = false; //监听移动的动作
  @override
  void initState() {
    super.initState();
    _controller.list.value = widget.list;
    notifyData();
  }

  void notifyData() {
    if (!TextUtil.isEmpty(widget.uuid)) {
      _presenter.getApproveDetail(widget.uuid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: (TextUtil.isEmpty(_controller.templateName.value)) ? '审批流程设置' : '编辑${_controller.templateName.value}规则',
            onBack: () {
              BoostNavigator.instance.pop();
            },
          ),
          body: Column(
            children: [
              Expanded(
                  child: SingleChildScrollView(
                child: Column(
                  children: [
                    Gaps.line,
                    Container(
                      alignment: Alignment.centerLeft,
                      padding: EdgeInsets.all(16),
                      color: Colours.white,
                      child: CommonUtils.getSimpleText("发起审批", 16, Colours.base_primary_text_body),
                    ),
                    InkWell(
                      child: Container(
                        width: double.infinity, // 将Container宽度设置为父级容器的宽度
                        color: Colours.base_primary_bg_page,
                        child: LoadAssetImage(
                          "common/icon_approve",
                          height: 60,
                          width: 60,
                        ),
                      ),
                      onTap: () {
                        if (_controller.list.length >= 10) {
                          BrnToast.show('最多可增加10条审批', context);
                        } else {
                          showSheetDialog(0);
                        }
                        // addNode(0);
                      },
                    ),
                    Obx(() {
                      var items = _controller.list.value;
                      return ReorderableListView(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        onReorder: (oldIndex, newIndex) {
                          setState(() {
                            if (oldIndex < newIndex) {
                              newIndex -= 1;
                            }
                            final item = items.removeAt(oldIndex);
                            items.insert(newIndex, item);
                          });
                          setState(() {
                            isDragging = false;
                          });
                        },
                        onReorderStart: (index) {
                          setState(() {
                            isDragging = true;
                          });
                        },
                        children: items.asMap().entries.map((entry) {
                          final index = entry.key;
                          final item = entry.value;
                          //	"node_type": "1",   //节点类型 1审批节点 2抄送节点
                          // 	"approver_type": "1",   //审批人类型 1系统(自动通过) 2指定成员 3指定上级 4指定角色
                          // 	"is_cosigned": "1",     //是否会签1是0否
                          // 	"superiors_level": "1",   //级别数
                          // 	"role_id": "1",   //角色id
                          // 	"user_list": ["1234", "123456"]   //用户uuid列表
                          return Column(
                            key: ValueKey(item),
                            children: [
                              Container(
                                padding: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
                                color: Colors.white,
                                child: Row(
                                  children: [
                                    InkWell(
                                      child: const LoadAssetImage(
                                        "common/icon_approve_delete",
                                        width: 20,
                                        height: 20,
                                      ),
                                      onTap: () {
                                        // 删除操作
                                        if (_controller.list.length <= 1) {
                                          Toast.show("请至少保留一个审批节点");
                                          return;
                                        }
                                        _controller.list.value.removeAt(index);
                                        _controller.updateList(_controller.list.value.toList());
                                      },
                                    ),
                                    Gaps.hGap10,
                                    Expanded(
                                      child: InkWell(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment: CrossAxisAlignment.start, // 文字靠右对齐
                                          children: [
                                            RichText(
                                                text: TextSpan(children: [
                                              TextSpan(
                                                text: item.nodeName,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: item.nodeType == "1" ? Colours.base_primary_jv : Colours.base_primary_blue, // 前面文字颜色
                                                  fontSize: 16, // 前面文字大小
                                                ),
                                              ),
                                              TextSpan(
                                                //item.approverType为1是自动审批，不显示这个
                                                text: (item.subNodeName == "" || item.subNodeName == null || "1" == item.approverType) ? "" : "(${item.subNodeName ?? ""})",
                                                style: const TextStyle(
                                                  color: Colours.base_primary_text_body, // 前面文字颜色
                                                  fontSize: 14, // 前面文字大小
                                                ),
                                              )
                                            ])),
                                            Visibility(child: CommonUtils.getSimpleText(item.userList?.map((e) => e.userName).toList().join("、"), 14, Colours.base_primary_text_title), visible: (item.userList?.isNotEmpty ?? false)),
                                          ],
                                        ),
                                        onTap: () {
                                          BoostNavigator.instance.push("appSettingsMainPage", arguments: {"item": item, 'style': item.nodeType}).then((value) {
                                            LogUtil.e("object--appSettingsMainPage--" + value.toString());
                                            if (value != null) {
                                              _controller.list.value[index] = (value as ApproveDetailList);
                                              handleData(value);
                                              _controller.updateList(_controller.list.value.toList());
                                            }
                                          });
                                        },
                                      ),
                                    ),
                                    IconButton(
                                      icon: LoadAssetImage(
                                        "base/icon_base_edit",
                                        height: 20,
                                        width: 20,
                                      ),
                                      onPressed: () {
                                        // 编辑操作
                                        BoostNavigator.instance.push("appSettingsMainPage", arguments: {"item": item, 'style': item.nodeType}).then((value) {
                                          LogUtil.e("object--appSettingsMainPage--" + value.toString());
                                          if (value != null) {
                                            _controller.list.value[index] = (value as ApproveDetailList);
                                            handleData(value);
                                            _controller.updateList(_controller.list.value.toList());
                                          }
                                        });
                                      },
                                    ),
                                    IconButton(
                                      icon: LoadAssetImage(
                                        "icon_fun",
                                        height: 20,
                                        width: 20,
                                      ),
                                      onPressed: () {},
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                width: double.infinity, // 将Container宽度设置为父级容器的宽度
                                color: Colours.base_primary_bg_page,
                                child: InkWell(
                                  child: const LoadAssetImage(
                                    "common/icon_approve",
                                    height: 60,
                                    width: 60,
                                  ),
                                  onTap: () {
                                    // addNode(index + 1);
                                    if (_controller.list.length >= 10) {
                                      BrnToast.show('最多只能添加10个节点', context);
                                    } else {
                                      showSheetDialog(index + 1);
                                    }
                                  },
                                ),
                              )
                            ],
                          );
                        }).toList(),
                      );
                    }),
                    Container(
                      padding: EdgeInsets.all(16),
                      color: Colours.white,
                      alignment: Alignment.centerLeft,
                      child: CommonUtils.getSimpleText("结束审批", 16, Colours.base_primary_text_body),
                    ),
                  ],
                ),
              )),
              MyBottomButtonOne(
                title: '提交',
                onPressed: () {
                  // HashMap<String,String> params = HashMap();
                  // params['uuid'] = widget.uuid;
                  // params["node_data"] = json.encode(_controller.list.value);
                  // _presenter.saveTemplate(params);
                  if (_controller.list.value.isEmpty) {
                    BrnToast.show('请选择节点', context);
                    return;
                  }
                  BoostNavigator.instance.pop(_controller.list.value);
                },
              )
            ],
          ),
        ));
  }

  void addNode(int index, int style) {
    //index 是当前的列表的下表，style 是审批节点还是抄送节点
    BoostNavigator.instance.push("appSettingsMainPage", arguments: {'style': "${style}"}).then((value) {
      if (value != null) {
        var newItem = value as ApproveDetailList;
        handleData(newItem);
        _controller.list.value.insert(index, newItem);
        _controller.updateList(_controller.list.value.toList());
      }
    });
  }

  void showSheetDialog(int position) {
    List<BrnCommonActionSheetItem> actions = [];
    actions.add(BrnCommonActionSheetItem(
      '审批节点',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ));
    actions.add(BrnCommonActionSheetItem(
      '抄送节点',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ));
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return BrnCommonActionSheet(
            actions: actions,
            clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
              addNode(position, index);
            },
          );
        });
  }

  void handleData(ApproveDetailList newItem) {
    if (newItem.nodeType == "1") {
      //审批
      String nodeName = "";
      switch (newItem.approverType) {
        case "1":
          nodeName = "自动审批";
          newItem.userList = [];
          break;
        case "2":
          nodeName = "指定成员审批";
          var userList = newItem.userList;
          newItem.userIdList = userList?.map((e) => (e.uuid ?? "")).toList() ?? [];
          break;
        case "3":
          nodeName = (newItem.superiorsLevelName ?? "") + "审批";
          newItem.userList = [];
          break;
        case "4":
          nodeName = (newItem.roleName ?? "") + "审批";
          newItem.userList = [];
          break;
      }
      newItem.nodeName = nodeName;
      if (newItem.isCosigned == "1") {
        newItem.subNodeName = "需所有成员同意";
      } else {
        newItem.subNodeName = "一名成员同意即可";
      }
    } else {
      String nodeName = "";
      switch (newItem.approverType) {
        case "1":
          nodeName = "自动抄送";
          newItem.userList = [];
          break;
        case "2":
          nodeName = "抄送指定成员";
          break;
        case "3":
          nodeName = "抄送" + (newItem.superiorsLevelName ?? "");
          newItem.userList = [];
          break;
        case "4":
          nodeName = "抄送" + (newItem.roleName ?? "");
          newItem.userList = [];
          break;
      }
      newItem.nodeName = nodeName;
      newItem.subNodeName = "";
    }
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ApproveEntryDepartPresenter();
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }

  @override
  void getApproveDetailData(ApproveDetailEntity? data) {
    _controller.updateName(data?.templateName);
    _controller.updateList(data?.list ?? []);
  }

  @override
  void saveTemplate(BaseUuidEntity? data) {
    Toast.show("操作成功");
    CommonUtils.finishPage();
  }
}
