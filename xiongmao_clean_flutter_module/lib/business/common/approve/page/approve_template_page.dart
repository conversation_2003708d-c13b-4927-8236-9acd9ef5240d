import 'dart:collection';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as p;
import 'package:image_pickers/image_pickers.dart';
import 'package:intl/intl.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_main_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_template_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_template_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/presenter/approve_main_presenter.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/presenter/approve_template_presenter.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_image_grid_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_button.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/decimal_textInput_formatter.dart';
import '../../../../util/qiniu/qiniu_utils.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../contract/persenter/contract_history_presenter.dart';
import '../../credit_inquiry/bean/custom_credit_entity.dart';
import '../../credit_inquiry/item/custom_credit_list_Item.dart';
import '../../quality_service/bean/base_media_entity.dart';
import '../bean/custom_template_data_entity.dart';
import '../bean/template_one_entity.dart';
import '../controller/custom_components_controller.dart';
import '../controller/material_controller.dart';
import '../iview/approve_main_iview.dart';
import '../iview/custom_comonents_iview.dart';
import '../iview/material_iview.dart';
import '../presenter/custom_comonents_presenter.dart';
import '../presenter/material_presenter.dart';

/// 自定义审批模版的节目，原生跳转进来
class ApproveTemplatePage extends StatefulWidget {
  String? uuid;
  String? application_no;
  String? is_system;
  String? title;
  String? application_status;

  ApproveTemplatePage({super.key, required this.uuid, required this.application_no, required this.is_system, required this.title, required this.application_status});

  @override
  _ApproveTemplatePageState createState() => _ApproveTemplatePageState();
}

class _ApproveTemplatePageState extends State<ApproveTemplatePage> with BasePageMixin<ApproveTemplatePage, PowerPresenter<dynamic>> implements ApproveTemplateView {
  final ApproveTemplateController _controller = ApproveTemplateController();

  late ApproveTemplatePresenter _presenter;

  final ScrollController _scrollController = ScrollController();

  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem(
      '拍照',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '相册',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    )
  ];

  void downListView() {
    // 滚动到最底部
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.uuid)) {
      _presenter.getTemplateOne(widget.uuid!, widget.application_no);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: widget.title ?? '申请',
      ),
      body: Obx(() => ListView.builder(
            shrinkWrap: true,
            controller: _scrollController,
            padding: EdgeInsets.only(bottom: 100),
            itemCount: _controller.fieldList.length,
            itemBuilder: (context, index) {
              return _itemBuilder(_controller.fieldList.value[index]);
            },
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
        child: Row(
          children: [
            // InkWell(
            //   child: Container(
            //     padding: const EdgeInsets.only(left: 20, right: 20, top: 7, bottom: 7),
            //     decoration: BoxDecoration(
            //       border: Border.all(color: Colours.base_primary, width: 1),
            //       borderRadius: BorderRadius.circular(4.0),
            //     ),
            //     child: CommonUtils.getSimpleText('存草稿', 14, Colours.base_primary),
            //   ),
            //   onTap: () {
            //     createTemplate(true);
            //   },
            // ),
            // Gaps.hGap10,
            Expanded(
                child: InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                decoration: BoxDecoration(
                  color: Colours.base_primary,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('确定', 14, Colours.white, textAlign: TextAlign.center),
              ),
              onTap: () {
                createTemplate(false);
              },
            ))
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ApproveTemplatePresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }

  Widget _itemBuilder(TemplateOneFieldList field) {
    return Container(
      color: Colors.white,
      margin: const EdgeInsets.only(top: 1),
      // padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
      child: getWight(field),
    );
  }

  Widget? getWight(TemplateOneFieldList data) {
    Widget? widget;
    switch (data.fieldType) {
      case 'text': //文本
        widget = (data.formatType == '1')
            ? BrnTextInputFormItem(
                title: data.fieldName ?? '模版名称',
                hint: (!TextUtil.isEmpty(data.promptText)) ? data.promptText : '请输入',
                controller: _controller.textEditingControllers[data.fieldFormName],
                isRequire: (data.isMust == '1') ? true : false,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(20), // 限制输入长度为20个字符
                ],
                onChanged: (newValue) {
                  if (data.fieldValue.isNotEmpty) {
                    data.fieldValue[0] = newValue;
                  } else {
                    data.fieldValue.add(newValue);
                  }
                },
              )
            : Column(
                children: [
                  BrnBaseTitle(
                    isRequire: (data.isMust == '1') ? true : false,
                    title: data.fieldName ?? '',
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 20, right: 20, top: 0, bottom: 20),
                    child: BrnInputText(
                      maxHeight: 200,
                      minHeight: 30,
                      minLines: 1,
                      maxLength: 150,
                      autoFocus: false,
                      bgColor: Colours.base_primary_bg_page,
                      textString: (data.fieldValue.isNotEmpty) ? data.fieldValue[0] : '',
                      textInputAction: TextInputAction.newline,
                      maxHintLines: 20,
                      hint: (!TextUtil.isEmpty(data.promptText)) ? data.promptText : '请输入',
                      padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                      onTextChange: (text) {
                        print(text);
                        if (data.fieldValue.isNotEmpty) {
                          data.fieldValue[0] = text;
                        } else {
                          data.fieldValue.add(text);
                        }
                      },
                      onSubmit: (text) {
                        print(text);
                      },
                    ),
                  ),
                ],
              );
        break;
      case 'number': //数字
        widget = BrnTextInputFormItem(
          title: data?.fieldName ?? '模版名称',
          hint: (!TextUtil.isEmpty(data?.promptText)) ? data?.promptText : '请输入',
          unit: (!TextUtil.isEmpty(data?.unitName)) ? data?.unitName : '',
          controller: _controller.textEditingControllers[data?.fieldFormName],
          isRequire: (data?.isMust == '1') ? true : false,
          inputType: BrnInputType.number,
          inputFormatters: [
            DecimalTextInputFormatter(decimalRange: (TextUtil.isEmpty(data?.formatType)) ? 0 : int.parse(data.formatType!)), // 限制小数点后两位
          ],
          onChanged: (newValue) {
            if (data.fieldValue.isNotEmpty) {
              data?.fieldValue[0] = newValue;
            } else {
              data?.fieldValue.add(newValue);
            }
          },
        );
        break;
      case 'date': //日期
        widget = BrnTextSelectFormItem(
          title: data.fieldName ?? '模版名称',
          hint: (!TextUtil.isEmpty(data.promptText)) ? data.promptText : '请选择',
          value: (data.fieldValue.isNotEmpty) ? data.fieldValue[0] : '',
          isRequire: (data.isMust == '1') ? true : false,
          onTap: () {
            BrnDatePicker.showDatePicker(
              themeData: BrnPickerConfig(
                pickerHeight: 300,
              ),
              context,
              pickerTitleConfig: BrnPickerTitleConfig.Default,
              pickerMode: BrnDateTimePickerMode.datetime,
              dateFormat: (data.formatType == '1') ? 'yyyy年,MMMM月,dd日' : 'yyyy年,MMMM月,dd日,HH时:mm分',
              onConfirm: (dateTime, list) {
                if (dateTime != null) {
                  // 确保 dateTime 不为 null
                  final formattedDate = DateUtil.formatDate(dateTime, format: (data.formatType == '1') ? 'yyyy/MM/dd' : 'yyyy/MM/dd HH:mm');

                  if (data.fieldValue.isNotEmpty) {
                    data.fieldValue[0] = formattedDate;
                  } else {
                    data.fieldValue.add(formattedDate);
                  }

                  _controller.notifyFieldList();
                }
              },
            );
          },
        );
        break;
      case 'single': //单选
        widget = BrnTextSelectFormItem(
          title: data.fieldName ?? '模版名称',
          hint: (!TextUtil.isEmpty(data.promptText)) ? data.promptText : '请选择',
          value: (data.fieldValue.isNotEmpty) ? data.fieldValue[0] : '',
          isRequire: (data.isMust == '1') ? true : false,
          onTap: () {
            showSingleDialog(data);
          },
        );
        break;
      case 'picture': //图片
        widget = Column(
          children: [
            BrnBaseTitle(
              title: data.fieldName ?? '模版名称',
              subTitle: (!TextUtil.isEmpty(data.promptText)) ? data.promptText : '',
              isRequire: (data.isMust == '1') ? true : false,
              customActionWidget: Visibility(
                visible: data.attachmentList.length < 9,
                child: InkWell(
                  child: Container(
                    child: const Center(child: Text('+ 增加照片', style: TextStyle(color: Colours.base_primary_text_title))),
                  ),
                  onTap: () {
                    showPhotoDialog(data);
                  },
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 10, right: 10),
              child: CustomImageGridView(
                imageUrls: getImageUrls(data.attachmentList),
                maxImageCount: 4,
                showAddButton: false,
                showDelButton: true,
                onDelCustomChanged: (index) {
                  data?.attachmentList.removeAt(index);
                  _controller.notifyFieldList();
                  // changeImageUrls(data?.attachmentList, updatedUrls);
                },
              ),
            ),
          ],
        );
        break;
      case 'attachment': //附件
        widget = Column(
          children: [
            BrnBaseTitle(
              title: data.fieldName ?? '模版名称',
              subTitle: (!TextUtil.isEmpty(data.promptText)) ? data.promptText : '',
              isRequire: (data.isMust == '1') ? true : false,
              customActionWidget: Visibility(
                visible: data.attachmentList.length < 9,
                child: InkWell(
                  child: Container(
                    child: const Center(child: Text('+ 增加附件', style: TextStyle(color: Colours.base_primary_text_title))),
                  ),
                  onTap: () {
                    showPickFile(data);
                  },
                ),
              ),
            ),
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: data?.attachmentList.length,
              itemBuilder: (context, index) {
                return Container(
                  padding: const EdgeInsets.only(left: 20, right: 20, bottom: 10),
                  child: Row(
                    children: [
                      Expanded(child: CommonUtils.getSimpleText(data?.attachmentList[index].fileName, 14, Colours.base_primary_text_title)),
                      InkWell(
                        onTap: () {
                          data?.attachmentList.removeAt(index);
                          _controller.notifyFieldList();
                        },
                        child: const LoadAssetImage(
                          "common/icon_approve_delete",
                          width: 26,
                          height: 26,
                        ),
                      ),
                    ],
                  ),
                );
              },
            )
          ],
        );
        break;
      case 'table': //表格
        widget = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: data.tableList.length,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    Container(
                      color: Colours.base_primary_bg_page,
                      padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText('${data.tableList[index].fieldName}${index + 1}', 14, Colours.base_primary_text_body),
                          Visibility(
                            visible: (data.isMust != '1' || data.tableList.length > 1),
                            child: InkWell(
                              child: CommonUtils.getSimpleText('删除', 14, Colours.base_primary_blue),
                              onTap: () {
                                BrnDialogManager.showConfirmDialog(context, title: "删除", cancel: '取消', confirm: '删除', message: "是否要删除该${data?.tableList[index].fieldName}${index + 1}", barrierDismissible: false, onConfirm: () {
                                  data.tableList.removeAt(index);
                                  _controller.notifyFieldList();

                                  Navigator.of(context, rootNavigator: true).pop();
                                }, onCancel: () {
                                  Navigator.of(context, rootNavigator: true).pop();
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: data?.tableList[index].child.length,
                      itemBuilder: (context, position) {
                        return _itemBuilder(data.tableList[index].child[position]);
                      },
                    )
                  ],
                );
              },
            ),
            Visibility(
              visible: data.tableList.length < 9,
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                child: InkWell(
                  child: CommonUtils.getSimpleText('+ 添加${data.fieldName}', 14, Colours.base_primary_blue),
                  onTap: () {
                    data.tableList.add(TemplateOneFieldList.fromJson((data.tableTemplateOriginal ?? TemplateOneFieldList()).toJson()));
                    _controller.notifyFieldList();
                    downListView();
                  },
                ),
              ),
            )
          ],
        );
        break;
    }
    return widget;
  }

  getImageUrls(List<CustomTemplateDataAttachmentList> list) {
    var medias = <BaseMediaEntity>[];
    for (CustomTemplateDataAttachmentList data in list) {
      medias.add(BaseMediaEntity(media_type: '1', media_url: data?.fileUrl));
    }
    return medias;
  }

  void changeImageUrls(List<CustomTemplateDataAttachmentList> list, List<BaseMediaEntity> updatedUrls) {
    print('图片改变---${updatedUrls.length}');
    // 创建一个包含所有更新后的URL的集合，用于快速查找。
    final Set<String> updatedUrlSet = Set.from(updatedUrls.map((e) => e.media_url));

    // 使用where来过滤出需要保留的项目，即存在于updatedUrls中的项目。
    final preservedItems = list.where((item) => updatedUrlSet.contains(item.fileUrl)).toList();

    // 更新原始列表为过滤后的列表。
    list
      ..clear()
      ..addAll(preservedItems);

    // 通知控制器字段列表发生变化。
    _controller.notifyFieldList();
  }

  /**
   * 单选处理
   */
  void showSingleDialog(TemplateOneFieldList data) {
    List<BrnCommonActionSheetItem> actions = [];
    for (String strs in data.option_list) {
      actions.add(BrnCommonActionSheetItem(
        strs,
        actionStyle: (data.fieldValue.isNotEmpty)
            ? (strs == data.fieldValue[0])
                ? BrnCommonActionSheetItemStyle.link
                : BrnCommonActionSheetItemStyle.normal
            : BrnCommonActionSheetItemStyle.normal,
      ));
    }

    // 展示actionSheet
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return BrnCommonActionSheet(
            actions: actions,
            clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
              String title = actionEle.title;
              // BrnToast.show("title: $title, index: $index", context);
              if (data.fieldValue.isNotEmpty) {
                data.fieldValue[0] = title;
              } else {
                data.fieldValue.add(title);
              }
              _controller.notifyFieldList();
            },
          );
        });
  }

  /// 图片处理
  void showPhotoDialog(TemplateOneFieldList data) async {
    if (data?.isMustCameraShoot == '1') {
      //强制拍照
      selectImage(0, data);
    } else {
      showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (BuildContext context) {
            return BrnCommonActionSheet(
              actions: actions,
              clickCallBack: (
                int index,
                BrnCommonActionSheetItem actionEle,
              ) {
                String title = actionEle.title;
                selectImage((index == 0) ? 1 : 0, data);
              },
            );
          });
    }
  }

  void selectImage(int imageSource, TemplateOneFieldList data) async {
    Media? photo;
    if (imageSource == 0) {
      List<Media> pickerPaths = await ImagePickers.pickerPaths();
      if (pickerPaths.isNotEmpty) {
        photo = pickerPaths[0];
      }
    } else {
      photo = await ImagePickers.openCamera();
    }
    if (photo == null) {
      return;
    }

    print('拿到图片了' + photo.path.toString());
    print('拿到图片了' + p.basename(photo.path ?? ""));
    //拿到图进行上传的操作
    BrnToast.show("上传中，请稍等", context);
    QiNiuUtils(photo.path, statusCallback: (status) {
      print("七牛上传状态---$status");
    }, successCallback: (keyUrl, hashUrl) {
      print("七牛上传成功来这里了--$keyUrl");
      var attach = CustomTemplateDataAttachmentList();

      attach.fileName = p.basename(photo?.path ?? "");
      attach.fileUrl = keyUrl;
      data?.attachmentList.add(attach);
      _controller.notifyFieldList();
    }, errorCallback: (error) {
      print("七牛上传失败--$error");
      BrnToast.show("上传失败", context);
    }).upload();
  }

  /// 文件处理
  Future<void> showPickFile(TemplateOneFieldList data) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();
    if (result != null) {
      print('选择的文件 ${result.files.single.path!}');
      BrnToast.show("上传中，请稍等", context);
      QiNiuUtils(result.files.single.path, statusCallback: (status) {
        print("七牛上传状态---$status");
      }, successCallback: (keyUrl, hashUrl) {
        print("七牛上传成功来这里了--$keyUrl");
        var attach = CustomTemplateDataAttachmentList();
        attach.fileName = result.files.single.name;
        attach.fileUrl = keyUrl;
        data?.attachmentList.add(attach);
        _controller.notifyFieldList();
      }, errorCallback: (error) {
        print("七牛上传失败--$error");
        BrnToast.show("上传失败", context);
      }).upload();
      // 处理选择的文件
    }
  }

  void createTemplate(bool is_draft) {
    // print('传递给后端的参数 ${_controller.fieldList.value}');
    var fieldList = <CustomTemplateDataEntity>[];

    if (_controller.fieldList.value.isNotEmpty) {
      for (var list in _controller.fieldList.value) {
        var child = <CustomTemplateDataEntity>[];

        //这里是子的child
        for (var subField in list.child) {
          var customTemplateDataEntity = CustomTemplateDataEntity();
          customTemplateDataEntity.key = subField.fieldFormName!;

          if (list.fieldType == 'single' && list.fieldValue.isNotEmpty) {
            customTemplateDataEntity.value.add('${subField.option_list.indexOf(subField.fieldValue[0])}');
          } else {
            customTemplateDataEntity.value = subField.fieldValue;
          }

          child.add(customTemplateDataEntity);
        }

        //这是外部的
        var template = CustomTemplateDataEntity();
        template.key = list.fieldFormName!;
        //如果是单选的情况下
        if (list.fieldType == 'single' && list.fieldValue.isNotEmpty) {
          template.value.add('${list.option_list.indexOf(list.fieldValue[0])}');
        } else {
          template.value = list.fieldValue;
        }

        if (list.fieldType == 'date' && (list.formatType == '2') && list.fieldValue.isNotEmpty) {
          template.value = list.fieldValue;
        }

        template.attachmentList = list.attachmentList;
        //这里是表格的情况下 需要创建新的内容，放到表格中
        if (list.fieldType == 'table') {
          debugPrint('提交的数据 ${list.tableList.length}');

          if (list.tableList != null && list.tableList.isNotEmpty) {
            bool hasNonEmptySublist = false;

            for (var subList in list.tableList) {
              if (subList != null && subList.child.isNotEmpty) {
                hasNonEmptySublist = true;
                break;
              }
            }

            if (hasNonEmptySublist) {
              for (var subList in list.tableList) {
                //这是一个大的
                if (subList != null && subList.child.isNotEmpty) {
                  var tableList = <CustomTemplateDataEntity>[]; //创建个数组来接受他
                  for (var childTableField in subList.child) {
                    var tableTemplate = CustomTemplateDataEntity();
                    tableTemplate.key = childTableField.fieldFormName;

                    if (childTableField.fieldType == 'single' && childTableField.fieldValue.isNotEmpty) {
                      tableTemplate.value.add('${childTableField.option_list.indexOf(childTableField.fieldValue[0])}');
                    } else {
                      tableTemplate.value = childTableField.fieldValue;
                    }

                    tableTemplate.attachmentList = childTableField.attachmentList;
                    tableList.add(tableTemplate);
                  }
                  template.tableList.add(tableList);
                }
              }
            }
          } else {
            // 如果 tableList 完全为空或者只包含空子数组，则不添加任何内容
            template.tableList = [];
          }
        }

        fieldList.add(template);
      }
    }
    HashMap<String, dynamic> params = HashMap();
    params['is_draft'] = is_draft ? '1' : '0'; //是否草稿

    // 0待完善 3已拒绝 4已撤回
    if (!TextUtil.isEmpty(widget.application_no) && widget.application_no != '4') {
      params['application_no'] = '${widget.application_no}';
    } else {
      if (!TextUtil.isEmpty(widget.uuid)) {
        params['template_uuid'] = '${widget.uuid}';
      }
    }
    debugPrint('提交的数据 ${json.encode(fieldList)}');
    params['field_list'] = json.encode(fieldList); //这就是表单
    _presenter.createTemplate(params);
  }

  @override
  void submit() {
    BrnToast.show('提交成功', context);
    BoostNavigator.instance.pop();
  }
}
