import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../project/item/project_manager_item_listview.dart';
import '../controller/custom_manager_controller.dart';
import '../item/custom_manager_item_listview.dart';
import '../iview/custom_manager_iview.dart';
import '../persenter/custom_manager_persenter.dart';

/// 客户管理
class CustomOnePage extends StatefulWidget {
  String? uuid = "";

  CustomOnePage({Key? key, this.uuid = ""}) : super(key: key);

  @override
  _CustomOnePageState createState() => _CustomOnePageState();
}

class _CustomOnePageState extends State<CustomOnePage> with BasePageMixin<CustomOnePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CustomOnePage> implements CustomMangerIView {
  CustomManagerPresenter? _presenter;

  final CustomManagerController _controller = CustomManagerController();

  @override
  void initState() {
    super.initState();
    _presenter?.requestCustomOne(widget.uuid ?? '');
    _presenter?.requestProjectManagerList(widget.uuid ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: '客户详情',
            actionName: _controller.oneDelName.value,
            onPressed: () {
              BrnDialogManager.showConfirmDialog(context, title: "删除", cancel: '取消', confirm: '删除', message: "删除后不可恢复，确定删除吗?", barrierDismissible: false, onConfirm: () {
                _presenter?.delCustom(widget.uuid ?? '');
                Navigator.of(context, rootNavigator: true).pop();
              }, onCancel: () {
                Navigator.of(context, rootNavigator: true).pop();
              });
            },
          ),
          body: ListView(
            children: [
              Container(
                margin: EdgeInsets.only(top: 10),
                padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonUtils.getSimpleText(_controller.data.value.customName ?? '', 17, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                            CommonUtils.getSimpleText(_controller.data.value.customFullName ?? '', 14, Colours.base_primary_text_caption),
                          ],
                        )),
                      ],
                    ),
                    Gaps.vGap10,
                    CommonUtils.getSimpleText('关键人：${(!TextUtil.isEmpty(_controller.data.value.contactPerson)) ? _controller.data.value.contactPerson : '-'}', 15, Colours.base_primary_text_title),
                    Gaps.vGap2,
                    CommonUtils.getSimpleText('关键人电话：${(!TextUtil.isEmpty(_controller.data.value.contactMobile)) ? _controller.data.value.contactMobile : '-'}', 15, Colours.base_primary_text_title),
                    Gaps.vGap2,
                    CommonUtils.getSimpleText('城市/区域：${(!TextUtil.isEmpty(_controller.data.value.cityName)) ? '${_controller.data.value.provinceName}${_controller.data.value.cityName}' : '-'}', 15, Colours.base_primary_text_title),
                    Gaps.vGap2,
                    CommonUtils.getSimpleText('详细地址：${(!TextUtil.isEmpty(_controller.data.value.address)) ? _controller.data.value.address : '-'}', 15, Colours.base_primary_text_title),
                    Gaps.vGap2,
                    CommonUtils.getSimpleText('市场负责人：${(!TextUtil.isEmpty(_controller.data.value.managerUserName)) ? _controller.data.value.managerUserName : '-'}', 15, Colours.base_primary_text_title),
                  ],
                ),
              ),
              Gaps.line,
              Container(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: CommonUtils.getSimpleText('备注：${(!TextUtil.isEmpty(_controller.data.value.remark)) ? _controller.data.value.remark : '-'}', 15, Colours.base_primary_text_title),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                child: CommonUtils.getSimpleText('合作项目', 14, Colours.base_primary_text_caption),
              ),

              Visibility(
                visible: _controller.listOne.value.isEmpty,
                child: Padding(
                  padding: const EdgeInsets.only(top: 100),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const LoadAssetImage(
                        "no_data",
                        width: 100,
                        height: 100,
                      ),
                      CommonUtils.getSimpleText('暂无数据', 14, Colours.base_primary_text_caption),
                    ],
                  ),
                ),
              ),

              ///客户详情中的列表展示 合作项目的展示
              Visibility(
                visible: _controller.listOne.value.isNotEmpty,
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _controller.listOne.value.length, // Example item count
                  itemBuilder: (context, index) {
                    return ProjectManagerListView(
                      data: _controller.listOne.value[index],
                      isSelected: false,
                      choose_uuid: '',
                      onClick: () {
                        BoostNavigator.instance.push('ProjectOneWebPage', arguments: {
                          'project_uuid': '${_controller.listOne.value[index].uuid}',
                          'project_name': '${!TextUtil.isEmpty(_controller.listOne.value[index].projectShortName) ? _controller.listOne.value[index].projectShortName : !TextUtil.isEmpty(_controller.listOne.value[index].projectName) ? _controller.listOne.value[index].projectName : ''}}',
                        });
                      },
                    );
                  },
                ),
              ),
            ],
          ),
          bottomNavigationBar: Container(
            color: Colors.white,
            padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
            child: Row(
              children: [
                Expanded(
                    child: InkWell(
                  child: Container(
                    padding: const EdgeInsets.only(left: 0, right: 0, top: 8, bottom: 8),
                    decoration: BoxDecoration(
                      color: Colours.base_primary,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: CommonUtils.getSimpleText('编辑', 16, Colours.white, textAlign: TextAlign.center),
                  ),
                  onTap: () {
                    BoostNavigator.instance.push('customSavePage', arguments: {'custom_one': _controller.data.value}).then((value) {
                      _presenter?.requestCustomOne(widget.uuid ?? '');
                      _presenter?.requestProjectManagerList(widget.uuid ?? '');
                    });
                  },
                )),
                Gaps.hGap10,
                Expanded(
                    child: InkWell(
                  child: Container(
                    padding: const EdgeInsets.only(left: 0, right: 0, top: 8, bottom: 8),
                    decoration: BoxDecoration(
                      color: Colours.base_primary,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: CommonUtils.getSimpleText('添加项目', 16, Colours.white, textAlign: TextAlign.center),
                  ),
                  onTap: () {
                    BoostNavigator.instance.push('ProjectSavePage', arguments: {'custom_uuid': widget.uuid}).then((value) => _presenter?.requestProjectManagerList(widget.uuid ?? ''));
                  },
                ))
              ],
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CustomManagerPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void saveStatus() {
    BoostNavigator.instance.pop();
  }
}
