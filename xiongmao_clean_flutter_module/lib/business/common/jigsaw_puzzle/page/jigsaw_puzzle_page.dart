import 'dart:io';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:xiongmao_clean_flutter_module/business/common/jigsaw_puzzle/bean/puzzle_item_data.dart';
import 'package:xiongmao_clean_flutter_module/business/common/jigsaw_puzzle/iview/jigsaw_puzzle_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/plan_task_one_new_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/net/net.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/permission_util.dart';
import '../bean/work_circle_entity.dart';
import '../controller/jigasw_puzzle_controller.dart';
import '../item/layout_puzzle_listview.dart';
import '../item/puzzle_item_listview.dart';
import '../presenter/jigsaw_puzzle_persenter.dart';

import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'dart:ui' as ui;

/// 拼图汇报
class JigsawPuzzlePage extends StatefulWidget {
  String? position = "";
  String? reporter_name = "";
  String? task_uuid = "";

  JigsawPuzzlePage({
    Key? key,
    this.position = "",
    this.reporter_name = "",
    this.task_uuid = "",
  }) : super(key: key);

  @override
  _JigsawPuzzlePageState createState() => _JigsawPuzzlePageState();
}

class _JigsawPuzzlePageState extends State<JigsawPuzzlePage> with BasePageMixin<JigsawPuzzlePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<JigsawPuzzlePage> implements JigsawPuzzleIView {
  JigsawPuzzlePagePresenter? _presenter;

  final JigsawPuzzlePageController _controller = Get.put(JigsawPuzzlePageController());

  @override
  void initState() {
    super.initState();

    _controller.reporter.value = widget.reporter_name ?? '';
    _controller.reporterDate.value = DateUtil.formatDate(DateTime.now(), format: 'yyyy年MM月dd日');

    if (!TextUtil.isEmpty(widget.task_uuid)) {
      _presenter?.requestCleanPlanTaskOne(widget.task_uuid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.white,
      appBar: MyAppBar(
        centerTitle: '拼图汇报',
        actionWidget: Container(
          margin: const EdgeInsets.only(right: 10),
          padding: const EdgeInsets.only(top: 4, bottom: 4, left: 10, right: 10),
          decoration: BoxDecoration(
            color: Colours.base_primary,
            borderRadius: BorderRadius.circular(4.0),
          ),
          child: CommonUtils.getSimpleText('分享', 14, Colours.white),
        ),
        onPressed: () {
          if (_controller.puzzleItems.isEmpty) {
            BrnToast.show('没数据呢，没办法生成图', context);
            return;
          }
          _controller.saveStatus.value = true;
          print('改变状态了 ${_controller.saveStatus.value}');
          _saveImage();
        },
      ),
      body: SingleChildScrollView(
        child: RepaintBoundary(
          key: _globalKey,
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Column(
              children: [
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.only(left: 18, right: 18),
                  child: Obx(
                        () =>
                        Row(
                          children: [
                            const Padding(
                              padding: EdgeInsets.only(top: 16),
                              child: LoadAssetImage(
                                'common/icon_common_puzzle_left',
                                height: 50,
                              ),
                            ),
                            Expanded(
                              child: InkWell(
                                child: Column(
                                  children: [
                                    CommonUtils.getSimpleText(
                                      _controller.title.value,
                                      43,
                                      Colours.base_primary,
                                      fontWeight: FontWeight.bold,
                                      textAlign: TextAlign.center,
                                    ),
                                    Gaps.vGap10,
                                    CommonUtils.getSimpleText(
                                      '工作现场照片汇总整理',
                                      14,
                                      Colours.base_primary,
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                                onTap: () {
                                  showAddTextDialog(_controller.title.value, (value) {
                                    if (!TextUtil.isEmpty(value)) {
                                      _controller.title.value = value;
                                    }
                                  });
                                },
                              ),
                            ),
                            const Padding(
                              padding: EdgeInsets.only(top: 16),
                              child: LoadAssetImage(
                                'common/icon_common_puzzle_right',
                                height: 50,
                              ),
                            ),
                          ],
                        ),
                  ),
                ),
                Gaps.vGap16,
                Container(
                  margin: const EdgeInsets.only(left: 18, right: 18),
                  padding: const EdgeInsets.only(left: 10, right: 10, top: 4, bottom: 4),
                  color: Colours.base_primary,
                  child: Obx(() =>
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: InkWell(
                              child: CommonUtils.getSimpleText(
                                '日期：${_controller.reporterDate.value}',
                                12,
                                Colours.white,
                              ),
                              onTap: () {
                                showDate();
                              },
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              child: CommonUtils.getSimpleText(
                                '汇报人：${_controller.reporter.value}',
                                12,
                                Colours.white,
                                textAlign: TextAlign.right,
                              ),
                              onTap: () {
                                showAddTextDialog(_controller.reporter.value, (value) {
                                  if (!TextUtil.isEmpty(value)) {
                                    _controller.reporter.value = value;
                                  }
                                });
                              },
                            ),
                          ),
                        ],
                      )),
                ),

                /// 以下是图片的内容
                Container(
                  margin: const EdgeInsets.only(
                    top: 10,
                    left: 18,
                    right: 18,
                  ),
                  color: Colours.base_primary_bg_page,
                  child: Column(
                    children: [
                      Container(
                        margin: const EdgeInsets.only(left: 10, right: 10, top: 10),
                        child: Obx(() {
                          return ListView.builder(
                            shrinkWrap: true,
                            padding: const EdgeInsets.only(bottom: 20),
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _controller.puzzleItems.value.length,
                            itemBuilder: (context, index) {
                              return PuzzleItemListView(
                                data: _controller.puzzleItems.value[index],
                                onTap: () {
                                  if (_controller.puzzleItems.value[index].type == 1) {
                                    showAddTextDialog(_controller.puzzleItems.value[index].text, (value) {
                                      if (!TextUtil.isEmpty(value)) {
                                        _controller.puzzleItems.value[index].text = value;
                                      } else {
                                        _controller.puzzleItems.value.removeAt(index);
                                      }
                                      _controller.notifyPuzzleList();
                                    });
                                  }
                                },
                                onPictureTap: () {
                                  showPhotoDialog(index);
                                },
                                onTextCloseTap: () {
                                  _controller.puzzleItems.value.removeAt(index);
                                  _controller.notifyPuzzleList();
                                },
                              );
                            },
                          );
                        }),
                      ),

                      /// 底部Footer
                      Obx(() =>
                          Visibility(
                            visible: _controller.puzzleItems.isNotEmpty && !_controller.saveStatus.value,
                            child: Container(
                              padding: const EdgeInsets.only(top: 10, bottom: 10),
                              margin: const EdgeInsets.only(left: 50, right: 50, top: 10, bottom: 30),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(2.0),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: InkWell(
                                      child: CommonUtils.getSimpleText('添加图片', 14, Colours.base_primary, textAlign: TextAlign.center),
                                      onTap: () => showPhotoDialog(-1),
                                    ),
                                  ),
                                  Gaps.vLine,
                                  Expanded(
                                    child: InkWell(
                                      child: CommonUtils.getSimpleText('添加文字', 14, Colours.base_primary, textAlign: TextAlign.center),
                                      onTap: () {
                                        showAddTextDialog('', (value) {
                                          _controller.addTextData(PuzzleItem(type: 1, text: value, images: []));
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )),
                      Obx(() =>
                          Visibility(
                            visible: _controller.puzzleItems.isEmpty,
                            child: Container(
                              padding: const EdgeInsets.only(bottom: 200, top: 50),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  InkWell(
                                    child: Container(
                                      padding: const EdgeInsets.only(left: 32, right: 32, top: 8, bottom: 8),
                                      decoration: const BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(2.0),
                                          bottomLeft: Radius.circular(2.0),
                                        ),
                                      ),
                                      child: CommonUtils.getSimpleText('添加图片', 14, Colours.base_primary),
                                    ),
                                    onTap: () => showPhotoDialog(-1),
                                  ),
                                  Gaps.vLine,
                                  InkWell(
                                    child: Container(
                                      padding: const EdgeInsets.only(left: 32, right: 32, top: 8, bottom: 8),
                                      decoration: const BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(2.0),
                                          bottomRight: Radius.circular(2.0),
                                        ),
                                      ),
                                      child: CommonUtils.getSimpleText('添加文字', 14, Colours.base_primary),
                                    ),
                                    onTap: () {
                                      showAddTextDialog('', (value) {
                                        _controller.addTextData(PuzzleItem(type: 1, text: value, images: []));
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: SizedBox(
        height: 83,
        child: Column(
          children: [
            Gaps.line,
            Container(
              color: Colors.white,
              height: 82,
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CommonUtils.getSimpleText('布局', 14, Colours.base_primary_text_title),
                  Gaps.hGap10,
                  Expanded(
                      child: ListView.builder(
                        shrinkWrap: true,
                        scrollDirection: Axis.horizontal,
                        itemCount: _controller.puzzleLayoutItems.length,
                        itemBuilder: (context, index) {
                          return Obx(() =>
                              LayoutPuzzleListView(
                                data: _controller.puzzleLayoutItems[index],
                                index: index,
                                selectedIndex: _controller.selectedIndex.value,
                                onTap: () {
                                  switch (index) {
                                    case 0:
                                      _controller.layoutGridCount.value = 1;
                                      break;
                                    case 1:
                                      _controller.layoutGridCount.value = 2;
                                      break;
                                    case 2:
                                      _controller.layoutGridCount.value = 3;
                                      break;
                                  }
                                  _controller.selectedIndex.value = index;
                                },
                              ));
                        },
                      ))
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  GlobalKey _globalKey = GlobalKey();

  Future<Uint8List?> _captureImage() async {
    RenderRepaintBoundary boundary = _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 3.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  void _saveImage() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          title: Text('图片保存中...'),
          content: Container(
            height: 50,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          ),
        );
      },
    );

    Future.delayed(Duration(seconds: 2), () async {
      Uint8List? imageBytes = await _captureImage();
      if (imageBytes != null) {
        // final result = await ImageGallerySaver.saveImage(imageBytes);
        // print('保存成功 $result ');
        // if (result != null) {
        //   Navigator.of(context, rootNavigator: true).pop(); // 关闭弹窗
        //   _controller.saveStatus.value = false;
        // } else {
        //   _controller.saveStatus.value = false;
        // }

        var filePath = "";
        // 获取手机存储（getTemporaryDirectory临时存储路径）
        Directory applicationDir = await getTemporaryDirectory();
        // 判断路径是否存在
        bool isDirExist = await Directory(applicationDir.path).exists();
        if (!isDirExist) Directory(applicationDir.path).create();
        // 直接保存，返回的就是保存后的文件
        File saveFile = await File(applicationDir.path + "${DateTime.now().toIso8601String()}.jpg").writeAsBytes(imageBytes);
        filePath = saveFile.path;
        final result = await ImageGallerySaver.saveFile(filePath);
        print('保存成功 $result ');
        print('保存成功这是本地的 $filePath ');
        if (result['isSuccess'] == true) {
          final result = await Share.shareXFiles([XFile(filePath)], text: '分享拼图');

          if (result.status == ShareResultStatus.success) {
            print('Thank you for sharing the picture!');
            dismissSaveDialog();
          } else {
            dismissSaveDialog();
          }
        } else {
          dismissSaveDialog();
        }
      } else {
        dismissSaveDialog();
      }
    });
  }

  void dismissSaveDialog() {
    Navigator.of(context, rootNavigator: true).pop(); // 关闭弹窗
    _controller.saveStatus.value = false;
  }

  /// 打开相册选择图片
  void showPhotoDialog(int position) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return BrnCommonActionSheet(
            actions: _controller.actionsAccount,
            clickCallBack: (int index, BrnCommonActionSheetItem actionEle) async {
              switch (index) {
                case 0:
                  Permission permission = await CommonUtils.getRequiredPhotosPermission();
                  permissionUtil.requestPermission(permission, tipMsg: "请先打开相册权限", requestSuccessFun: () {
                    openLocationAlbum(position);
                  });
                  break;
                case 1: //选择已有照片
                  BoostNavigator.instance.push("workImagesListPage").then((value) {
                    print('选择照片过来的数据 - $value');
                    if (value != null) {
                      selectImagesResult(position, value);
                    }
                  });
                  break;
                case 2:
                  permissionUtil.requestPermission(Permission.camera, tipMsg: "拍摄照片需要获取您的相机权限", requestSuccessFun: () {
                    openCamera(position);
                  });
                  break;
              }
            },
          );
        });
  }

  /// 打开本地相册
  void openLocationAlbum(int position) async {
    List<Media> _listImagePaths = await ImagePickers.pickerPaths(galleryMode: GalleryMode.image, selectCount: 99, showGif: false, showCamera: false);
    if (_listImagePaths.isNotEmpty) {
      // var path = _listImagePaths[0].path;
      List<String> _images = [];
      for (Media data in _listImagePaths) {
        _images.add(data.path ?? '');
      }
      if (position == -1) {
        _controller.addPhotoData(PuzzleItem(type: 2, text: '', images: _images));
      } else {
        //否则就是个i已有的Grid数组中增加数据
        _controller.puzzleItems[position].images.addAll(_images);
        _controller.notifyPuzzleList();
      }
    }
  }

  void openCamera(int position) async {
    // 将 Permission.camera 替换为你想要检查的权限类型
    var status = await Permission.camera.status;
    if (status.isRestricted || status.isDenied || status.isPermanentlyDenied) {
      BrnDialogManager.showConfirmDialog(context, title: "使用该功能，需要用到相机权限来拍摄图片，请授权",
          cancel: '取消',
          confirm: '去授权',
          onConfirm: () {
            _openCamera(position);
            Navigator.of(context, rootNavigator: true).pop();
          },
          onCancel: () {
            Navigator.of(context, rootNavigator: true).pop();
          });
    } else {
      // Request the permission
      var result = await Permission.camera.request();
      if (result.isGranted) {
        // Permission was granted, proceed with opening the camera
        await _openCamera(position);
      } else {
        print("相机权限被拒绝");
      }
    }
  }

  Future<void> _openCamera(int position) async {
    var photo = await ImagePickers.openCamera();
    if (photo != null) {
      if (position == -1) {
        List<String> _images = [];
        _images.add(photo.path ?? '');
        _controller.addPhotoData(PuzzleItem(type: 2, text: '', images: _images));
      } else {
        //否则就是个i已有的Grid数组中增加数据
        _controller.puzzleItems[position].images.add(photo.path ?? "");
        _controller.notifyPuzzleList();
      }
    }
  }

  /**
   * 选已上传的图片后，进行处理
   */
  void selectImagesResult(int position, Object value) {
    print('选择了网络图片');
    List<WorkCircleListList> lists = value as List<WorkCircleListList>;
    print('选择了网络图片 -> ${lists.length}');
    if (position == -1) {
      List<String> _images = [];
      for (WorkCircleListList item in lists) {
        _images.add(item.mediaUrl ?? '');
      }
      _controller.addPhotoData(PuzzleItem(type: 2, text: '', images: _images));
    } else {
      for (WorkCircleListList item in lists) {
        _controller.puzzleItems[position].images.add(item.mediaUrl ?? '');
      }
      _controller.notifyPuzzleList();
    }
  }

  /**
   * 修改日期
   */
  void showDate() {
    BrnDatePicker.showDatePicker(
      themeData: BrnPickerConfig(
        pickerHeight: 300,
      ),
      context,
      pickerTitleConfig: BrnPickerTitleConfig.Default,
      pickerMode: BrnDateTimePickerMode.date,
      dateFormat: 'yyyy年,MMMM月,dd日',
      onConfirm: (dateTime, list) {
        _controller.reporterDate.value = DateUtil.formatDate(dateTime, format: 'yyyy年MM月dd日');
      },
    );
  }

  /**
   * 添加文本内容
   */
  void showAddTextDialog(String? text, Function(String) onConfirm) {
    BrnMiddleInputDialog(
        title: '文字',
        hintText: '请输入正文内容',
        cancelText: '取消',
        confirmText: '确定',
        maxLength: 1000,
        maxLines: 2,
        barrierDismissible: false,
        inputEditingController: TextEditingController()
          ..text = text ?? '',
        textInputAction: TextInputAction.done,
        onConfirm: (value) {
          onConfirm(value);
          Navigator.of(context, rootNavigator: true).pop();
        },
        onCancel: () {
          Navigator.of(context, rootNavigator: true).pop();
        }).show(context);
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = JigsawPuzzlePagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void dispose() {
    super.dispose();
    _controller.puzzleItems.value.clear();
  }

  @override
  void updateCleanPlanTaskOneEntityEntity(PlanTaskOneNewEntity data) {
    //工单的内容，数据填充到界面上
    if (!TextUtil.isEmpty(data.workContent)) {
      _controller.addTextData(PuzzleItem(type: 1, text: '工作内容：\n${data.workContent}', images: []));
    }
    //任务图
    if (data.mediaList!.isNotEmpty) {
      List<String> _mediaImages = [];
      for (PlanTaskOneNewMediaList item in data.mediaList!) {
        _mediaImages.add(item.mediaUrl ?? '');
      }
      _controller.addPhotoData(PuzzleItem(type: 2, text: '', images: _mediaImages));
    }
    //处理图
    if (data.dealMediaList!.isNotEmpty) {
      List<String> _dealMediaImages = [];
      for (PlanTaskOneNewDealMediaList item in data.dealMediaList!) {
        _dealMediaImages.add(item.mediaUrl ?? '');
      }
      _controller.addPhotoData(PuzzleItem(type: 2, text: '', images: _dealMediaImages));
    }
  }
}
