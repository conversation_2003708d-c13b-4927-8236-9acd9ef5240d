import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_search_view.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/project_manager_controller.dart';
import '../item/project_manager_item_listview.dart';
import '../iview/project_manager_iview.dart';
import '../persenter/project_manager_persenter.dart';

/// 项目管理
class ProjectManagerPage extends StatefulWidget {
  String? choose_uuid = "";

  bool isSelected = false;

  ProjectManagerPage({Key? key, this.choose_uuid = "", required this.isSelected}) : super(key: key);

  @override
  _ProjectManagerPageState createState() => _ProjectManagerPageState();
}

class _ProjectManagerPageState extends State<ProjectManagerPage> with BasePageMixin<ProjectManagerPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ProjectManagerPage> implements ProjectMangerIView {
  ProjectManagerPresenter? _presenter;

  final ProjectManagerController _controller = ProjectManagerController();

  VoidCallback? refreshListener;

  @override
  void initState() {
    super.initState();
    _onRefresh();

    refreshListener ??= BoostChannel.instance.addEventListener("refresh", (key, arguments) async {
      _onRefresh();
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: widget.isSelected ? '选择项目' : '项目管理',
        actionName: widget.isSelected ? '添加' : '',
        onPressed: () {
          ///如果是true的情况下，去新增
          if (widget.isSelected) {
            BoostNavigator.instance.push('ProjectSavePage').then((value) => _onRefresh());
          }
        },
      ),
      body: Column(
        children: [
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
            child: CustomSearchView(
              hint: '请输入内容搜索项目',
              onTextChanged: (text) {
                _controller.searchText.value = text;
                _onRefresh();
              },
            ),
          ),

          ///列表
          Obx(() => Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.list.value.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                padding: const EdgeInsets.only(top: 10),
                hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
                itemBuilder: (_, index) {
                  return ProjectManagerListView(
                    data: _controller.list[index],
                    isSelected: widget.isSelected,
                    choose_uuid: widget.choose_uuid ?? '',
                    onClick: () {
                      if (widget.isSelected) {
                        BoostNavigator.instance.pop(_controller.list.value[index]);
                        return;
                      }
                      BoostNavigator.instance.push('ProjectOneWebPage', arguments: {
                        'project_uuid': '${_controller.list.value[index].uuid}',
                        'project_name': '${!TextUtil.isEmpty(_controller.list.value[index].projectShortName) ? _controller.list.value[index].projectShortName : !TextUtil.isEmpty(_controller.list.value[index].projectName) ? _controller.list.value[index].projectName : ''}',
                      }).then((value) => _onRefresh());
                    },
                  );
                },
              )))
        ],
      ),

      ///这里是编辑 添加项目
      bottomNavigationBar: Visibility(
        visible: !widget.isSelected,
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
          child: BrnBigMainButton(
            themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
            title: '+ 添加项目',
            onTap: () {
              BoostNavigator.instance.push('ProjectSavePage').then((value) => _onRefresh());
            },
          ),
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ProjectManagerPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void updateStatus() {}

  @override
  void dispose() {
    super.dispose();
    refreshListener?.call();
  }
}
