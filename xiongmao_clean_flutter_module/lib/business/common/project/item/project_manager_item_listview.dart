import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/project_manager_entity.dart';

class ProjectManagerListView extends StatelessWidget {
  ProjectManagerList data;

  String choose_uuid;
  bool isSelected;

  final Function onClick;

  ProjectManagerListView({required this.data, required this.isSelected, required this.choose_uuid, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: BoxDecoration(
          color: Colours.white,
          borderRadius: BorderRadius.circular(10.0),
        ),
        margin: const EdgeInsets.only(
          left: 10,
          right: 10,
          bottom: 10,
        ),
        padding: const EdgeInsets.only(
          top: 10,
          left: 16,
          right: 16,
          bottom: 10,
        ), // 设置内边距为16.0
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                            child: CommonUtils.getSimpleText(
                                !TextUtil.isEmpty(data.projectShortName)
                                    ? data.projectShortName
                                    : !TextUtil.isEmpty(data.projectName)
                                        ? data.projectName
                                        : '',
                                16,
                                Colours.base_primary_text_title,
                                fontWeight: FontWeight.bold)),
                        Visibility(
                          visible: !isSelected && !TextUtil.isEmpty(data.projectCatParentName) && !TextUtil.isEmpty(data.projectCatName),
                          child: Container(
                            padding: const EdgeInsets.only(
                              left: 4,
                              right: 4,
                            ),
                            margin: const EdgeInsets.only(
                              top: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colours.transparent,
                              borderRadius: BorderRadius.circular(2.0),
                              border: Border.all(color: Colours.base_primary, width: 0.7),
                            ),
                            child: CommonUtils.getSimpleText(buildTypeName(data), 13, Colours.base_primary),
                          ),
                        ),
                      ],
                    ),
                    Gaps.vGap2,

                    ///立项/定岗/在岗
                    CommonUtils.getSimpleText('立项${data.contractHumanNum ?? 0}人/定岗${data.totalJobNum ?? 0}人/在岗${data.onJobNum ?? 0}人', 13, Colours.base_primary_text_caption),
                  ],
                )),
                Visibility(
                  visible: isSelected,
                  child: LoadAssetImage(
                    (data.uuid == choose_uuid) ? "icon_check" : "icon_uncheck",
                    width: 20,
                    height: 20,
                  ),
                ),
              ],
            ),
            Gaps.vGap6,
            Gaps.line,
            Gaps.vGap6,
            Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText('运营负责人：${(TextUtil.isEmpty(data.managerUserName) ? " -" : data.managerUserName)}', 14, Colours.base_primary_text_title)),
                Expanded(child: CommonUtils.getSimpleText('合同公司：${(TextUtil.isEmpty(data.contractCompanyName) ? " -" : data.contractCompanyName)}', 14, Colours.base_primary_text_title, overflow: TextOverflow.ellipsis)),
              ],
            ),
            Gaps.vGap6,
            Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText('服务面积：${(TextUtil.isEmpty(data.square) ? " -" : data.square)}', 14, Colours.base_primary_text_title)),
                Expanded(child: CommonUtils.getSimpleText('创建时间：${(TextUtil.isEmpty(data.createTime) ? " -" : data.createTime)}', 14, Colours.base_primary_text_title, maxLines: 1, overflow: TextOverflow.ellipsis)),
              ],
            ),

            Gaps.vGap6,

            Row(
              children: [
                CommonUtils.getSimpleText('客户：', 14, Colours.base_primary_text_title),
                Gaps.hGap4,
                CommonUtils.getSimpleText(!TextUtil.isEmpty(data.customName) ? data.customName : '-', 14, Colours.base_primary_text_title),
              ],
            ),
            Gaps.vGap6,

            ///备注
            Row(
              children: [
                CommonUtils.getSimpleText('备注', 14, Colours.base_primary_text_title),
                Gaps.hGap4,
                CommonUtils.getSimpleText(!TextUtil.isEmpty(data.remark) ? data.remark : '-', 14, Colours.base_primary_text_title),
              ],
            ),
            Gaps.vGap10,
            Gaps.line,
            Gaps.vGap10,
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    child: CommonUtils.getSimpleText('考勤规则', 16, Colours.base_primary, textAlign: TextAlign.center),
                    onTap: () {
                      BoostNavigator.instance.push('ProjectAttendanceManagerRulesPage', arguments: {
                        'project_uuid': '${data.uuid}',
                        'project_name': '${(!TextUtil.isEmpty(data.projectShortName) ? data.projectShortName : data.projectName)}',
                      });
                    },
                  ),
                ),
                Gaps.vLine,
                Expanded(
                  child: InkWell(
                    child: CommonUtils.getSimpleText('清洁区域', 16, Colours.base_primary, textAlign: TextAlign.center),
                    onTap: () {
                      BoostNavigator.instance.push('regionPage', arguments: {
                        'project_uuid': data.uuid,
                        'project_name': (!TextUtil.isEmpty(data.projectShortName) ? data.projectShortName : data.projectName),
                      });
                    },
                  ),
                ),
                Gaps.vLine,
                Expanded(
                  child: InkWell(
                    child: CommonUtils.getSimpleText('清洁计划', 16, Colours.base_primary, textAlign: TextAlign.center),
                    onTap: () {
                      BoostNavigator.instance.push('workPlanPage', arguments: {
                        'project_uuid': data.uuid,
                        'project_name': (!TextUtil.isEmpty(data.projectShortName) ? data.projectShortName : data.projectName),
                        'type': '1',
                      });
                    },
                  ),
                ),
                Gaps.vLine,
                Expanded(
                  child: InkWell(
                    child: CommonUtils.getSimpleText('更多', 16, Colours.base_primary, textAlign: TextAlign.center),
                    onTap: () {
                      List<BrnCommonActionSheetItem> actions = [];
                      actions.add(BrnCommonActionSheetItem(
                        '巡检计划',
                        actionStyle: BrnCommonActionSheetItemStyle.normal,
                      ));
                      actions.add(BrnCommonActionSheetItem(
                        '培训计划',
                        actionStyle: BrnCommonActionSheetItemStyle.normal,
                      ));

                      // 展示actionSheet
                      showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: Colors.transparent,
                          builder: (BuildContext context) {
                            return BrnCommonActionSheet(
                              actions: actions,
                              clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
                                BoostNavigator.instance.push('workPlanPage', arguments: {
                                  'project_uuid': data.uuid,
                                  'project_name': (!TextUtil.isEmpty(data.projectShortName) ? data.projectShortName : data.projectName),
                                  'type': (index == 0) ? '2' : '3',
                                });
                              },
                            );
                          });
                    },
                  ),
                ),
              ],
            )
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }

  String maskPhoneNumber(String phoneNumber) {
    if (phoneNumber.length != 11) {
      throw ArgumentError('Phone number must be 11 digits long');
    }
    // 使用正则表达式替换中间四位
    return phoneNumber.replaceRange(3, 7, '****');
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );

    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      throw 'Could not launch $launchUri';
    }
  }

  //针对类型做拼接
  String buildTypeName(ProjectManagerList data) {
    StringBuffer stringBuffer = StringBuffer();
    if (!TextUtil.isEmpty(data.projectCatParentName)) {
      stringBuffer.write(data.projectCatParentName);
      stringBuffer.write('-');
    }
    if (!TextUtil.isEmpty(data.projectCatName)) {
      stringBuffer.write(data.projectCatName);
    }
    return stringBuffer.toString();
  }
}
