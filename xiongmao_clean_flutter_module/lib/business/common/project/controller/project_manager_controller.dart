import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../bean/project_manager_entity.dart';

class ProjectManagerController extends GetxController {
  var uuid = "".obs;

  var searchText = "".obs;

  var listTotal = "0".obs;

  var invertList = <ProjectManagerList>[].obs;

  ///详情的项目列表
  var list = <ProjectManagerList>[].obs;

  var totalNumber = "0".obs;

  void initMyList(int total, List<ProjectManagerList> value) {
    totalNumber.value = total.toString();
    list.value = value.toList();
  }

  void updateMyList(List<ProjectManagerList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///详情
  var data = ProjectManagerList().obs;

  void updateCustomOne(ProjectManagerList entity) {
    data.value = entity;
  }

  String getSelectedUuids() {
    return list.value.where((item) => item.isGroupClockIn == '1').map((item) => item.uuid).join(',');
  }
}
