import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_switch.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../../../widgets/state_layout.dart';
import '../../approve/bean/base_choose_string.dart';
import '../controller/notice_message_controller.dart';
import '../item/notice_message_item_listview.dart';
import '../iview/notice_messsage_iview.dart';
import '../presenter/notice_message_persenter.dart';

/**
 * 系统消息
 */
class NoticeMessagePage extends StatefulWidget {
  String? user_name = "";

  NoticeMessagePage({Key? key, this.user_name = ""}) : super(key: key);

  @override
  _NoticeMessagePageState createState() => _NoticeMessagePageState();
}

class _NoticeMessagePageState extends State<NoticeMessagePage> with BasePageMixin<NoticeMessagePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<NoticeMessagePage> implements NoticeMessageIView {
  NoticeMessagePagePresenter? _presenter;

  final NoticeMessagePageController _controller = NoticeMessagePageController();

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  void initState() {
    super.initState();
    _onRefresh();
    _presenter?.sendRead();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '系统通知',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: Obx(() => MyRefreshListView(
            itemCount: _controller.list.length,
            onRefresh: _onRefresh,
            loadMore: _loadMore,
            hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
            itemBuilder: (_, index) {
              // return WorkPostManagerListItem(provider.list[index], provider, _player, index);
              return NoticeMessageItemListView(data: _controller.list[index]);
            },
          )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = NoticeMessagePagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => false;
}
