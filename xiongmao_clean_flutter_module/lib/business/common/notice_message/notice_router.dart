import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_history_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_market_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/notice_message/page/notice_message_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/todo/page/todo_page.dart';

import '../../../net/http_config.dart';

/// 系统通知显示的内容
const noticeMessagePage = "noticeMessagePage";

/// 系统通知
Map<String, FlutterBoostRouteFactory> noticeMessageRouterMap = {
  noticeMessagePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return NoticeMessagePage();
        });
  },
};

