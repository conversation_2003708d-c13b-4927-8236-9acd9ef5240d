import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/qiniu/qiniu_utils.dart';
import '../../../../widgets/load_image.dart';
import '../bean/account_bank_entity.dart';
import '../controller/balance_account_controller.dart';
import '../iview/balance_account_iview.dart';
import '../presenter/balance_account_persenter.dart';

class WithdrawalAccountPage extends StatefulWidget {
  String? position = "";

  WithdrawalAccountPage({super.key, required this.position});

  @override
  _WithdrawalAccountPageState createState() => _WithdrawalAccountPageState();
}

class _WithdrawalAccountPageState extends State<WithdrawalAccountPage> with BasePageMixin<WithdrawalAccountPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WithdrawalAccountPage> implements BalanceAccountIView {
  final BalanceAccountController _controller = BalanceAccountController();

  BalanceAccountPresenter? _presenter;

  var _aliNo = '';

  var _bankUser = '';

  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem(
      '拍照',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '相册',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    )
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '收款账号',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: Column(
        children: [
          Visibility(
              visible: widget.position == '0',
              child: Column(
                children: [
                  BrnTextInputFormItem(
                    title: "支付宝姓名",
                    isRequire: true,
                    hint: "请输入",
                    onChanged: (newValue) {
                      setState(() {
                        _bankUser = newValue;
                      });
                    },
                  ),
                  Gaps.line,
                  BrnTextInputFormItem(
                    title: "支付宝账号",
                    isRequire: true,
                    hint: "请输入",
                    onChanged: (newValue) {
                      _aliNo = newValue;
                    },
                  ),
                ],
              )),
          Visibility(
              visible: widget.position == '1',
              child: Column(
                children: [
                  BrnTextInputFormItem(
                    title: "持卡人姓名",
                    isRequire: true,
                    hint: "请输入",
                    onChanged: (newValue) {
                      setState(() {
                        _bankUser = newValue;
                      });
                    },
                  ),
                  Gaps.line,
                  BrnBaseTitle(
                    title: "银行卡号",
                    isRequire: true,
                    customActionWidget: Container(
                      width: 220,
                      child: Row(
                        children: [
                          Expanded(
                              child: TextField(
                            decoration: const InputDecoration(
                              isCollapsed: true,
                              hintText: '请输入',
                              hintStyle: TextStyle(color: Colours.base_primary_text_hint, fontSize: 14),
                              border: InputBorder.none, // 移除输入框边框
                            ),
                            keyboardType: TextInputType.number,
                            // 限制只能输入数字
                            style: const TextStyle(color: Colours.base_primary_text_title, fontSize: 14),
                            maxLines: 1,
                            textAlign: TextAlign.right,
                            // 设置最大行数为1
                            onChanged: (newValue) {
                              // 处理输入内容
                              setState(() {
                                _controller.bankNo.value = newValue;
                              });
                            },
                          )),
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 10,
                            ),
                            child: InkWell(
                              child: const LoadAssetImage(
                                "icon_camera",
                                width: 20,
                                height: 20,
                              ),
                              onTap: () {
                                openCamera();
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    onTip: () {
                      BrnToast.show("点击触发回调_onTip", context);
                    },
                  ),
                  // BrnTextInputFormItem(
                  //   title: "银行卡号",
                  //   isRequire: true,
                  //   hint: "请输入",
                  //   onChanged: (newValue) {
                  //     setState(() {
                  //       _controller.bankNo.value = newValue;
                  //     });
                  //   },
                  // ),
                  Gaps.line,
                  BrnTextSelectFormItem(
                    title: "开户银行",
                    isRequire: true,
                    hint: "请选择",
                    value: _controller.bankName.value,
                    onTap: () {
                      BoostNavigator.instance.push("withdrawalAccountBankPostPage").then((value) {
                        // 确保 value 是 AccountBankList 类型
                        setState(() {
                          if (value is AccountBankList) {
                            AccountBankList accountBankList = value;
                            _controller.bankName.value = accountBankList.name ?? "";
                            _controller.bankCode.value = accountBankList.code ?? "";
                          }
                        });
                      });
                    },
                  ),
                ],
              )),
          Gaps.line,
          Gaps.vGap20,
          MyBottomButtonOne(
              title: "下一步",
              isNeedBg: false,
              isNeedDivideLine: false,
              onPressed: () {
                print('输入的姓名 ${_bankUser}');
                BoostNavigator.instance.push("withdrawalAccountBindPostPage", arguments: {
                  "bankUser": _bankUser,
                  "account_no": (widget.position == '0' ? _aliNo : _controller.bankNo.value),
                  "account_type": (widget.position == '0' ? '2' : '1'),
                  "bankCode": _controller.bankCode.value,
                });
              }),
        ],
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = BalanceAccountPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void getSmsCode() {}

  ///打开相机查找图片，并自动识别图片
  Future<void> openCamera() async {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return BrnCommonActionSheet(
            actions: actions,
            clickCallBack: (
              int index,
              BrnCommonActionSheetItem actionEle,
            ) {
              String title = actionEle.title;
              // BrnToast.show("title: $title, index: $index", context);
              selectImage((index == 0) ? 1 : 0);
            },
          );
        });
  }

  //0是拍照，1是相册选
  Future<void> selectImage(int imageSource) async {
    Media? photo;
    if(imageSource==0){
      List<Media> pickerPaths = await ImagePickers.pickerPaths();
      if(pickerPaths.isNotEmpty){
        photo  = pickerPaths[0];
      }
    }else{
      photo = await ImagePickers.openCamera();
    }
   if(photo==null){
     return;
   }
    print('拿到图片了' + photo.path.toString());
    //拿到图进行上传的操作
    BrnToast.show("识别中，请稍等", context);
    QiNiuUtils(photo.path, statusCallback: (status) {
      print("七牛上传状态---$status");
    }, successCallback: (keyUrl, hashUrl) {
      print("七牛上传成功--$keyUrl");
      _presenter?.requestAutoOcrBank(keyUrl!);
    }, errorCallback: (error) {
      print("七牛上传失败--$error");
      BrnToast.show("上传失败", context);
    }).upload();
    }
}
