import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../approve/bean/base_choose_string.dart';
import '../bean/staff_detail_entity.dart';

class AddStaffSalaryInfoController extends GetxController {
  var uuid = ''.obs;
  var applicationNo = ''.obs;

  ///关系位置
  var insureIndex = 0.obs;

  ///参保方案
  var insureDesc = '未匹配到参保方案，请向上级反馈'.obs;

  ///关系列表
  var insureList = [
    BaseChooseString('商业保险'),
    BaseChooseString('社保'),
    BaseChooseString('不参保'),
  ].obs;

  ///基础薪资
  var salaryController = TextEditingController();

  ///填充信息
  void fillData(StaffDetailEntity staffDetailEntity) {
    StaffDetailSalaryInsurance? contact = staffDetailEntity.salaryInsurance;
    if (contact != null) {
      salaryController.text = contact.salary ?? '0';
      if (!TextUtil.isEmpty(contact.insuranceProductName)) {
        insureDesc.value = contact.insuranceProductName!;
      } else {
        insureDesc.value = '未匹配到参保方案，请向上级反馈';
      }

      if ('1' == contact.insuranceType) {
        insureIndex.value = 2;
      } else if ('2' == contact.insuranceType) {
        insureIndex.value = 1;
      } else if ('3' == contact.insuranceType) {
        insureIndex.value = 0;
      }
    }
  }
}
