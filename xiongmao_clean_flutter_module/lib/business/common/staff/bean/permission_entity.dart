import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/holiday_data_entity.g.dart';
import 'dart:convert';

import '../../../../generated/json/company_contact_entity.g.dart';
import '../../../../generated/json/permission_entity.g.dart';
export 'package:xiongmao_clean_flutter_module/generated/json/holiday_data_entity.g.dart';

@JsonSerializable()
class PermissionEntity {
	List<PermissionData>? list;

	PermissionEntity();

	factory PermissionEntity.fromJson(Map<String, dynamic> json) => $PermissionEntityFromJson(json);

	Map<String, dynamic> toJson() => $PermissionEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class PermissionData {
	String? id;
	String? name;
	String? role_desc;

	PermissionData();

	factory PermissionData.fromJson(Map<String, dynamic> json) => $PermissionDataFromJson(json);

	Map<String, dynamic> toJson() => $PermissionDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}
