import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/constant.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/log_utils.dart';
import '../../../../util/toast_utils.dart';
import '../../../../widgets/my_app_bar.dart';
import '../../../../widgets/xm_webview/xm_js_msg_call.dart';
import '../../../../widgets/xm_webview/xm_web_controller.dart';
import '../../../../widgets/xm_webview/xm_webview.dart';
import '../controller/staff_one_controller.dart';
import '../iview/permiss_iview.dart';
import '../iview/staff_one_iview.dart';
import '../persenter/staff_one_persenter.dart';

///员工档案详情
class StaffOneWebPage extends StatefulWidget {
  String uuid;

  StaffOneWebPage({required this.uuid});

  @override
  _StaffOneWebPageState createState() => _StaffOneWebPageState();
}

class _StaffOneWebPageState extends State<StaffOneWebPage> with BasePageMixin<StaffOneWebPage, PowerPresenter<dynamic>> implements StaffOneIView {
  StaffOnePresenter? _presenter;

  final StaffOneController _controller = StaffOneController();

  late XmWebController controller;

  String url = "";
  bool _webViewReady = false;

  @override
  void initState() {
    super.initState();
    _controller.uuid.value = widget.uuid;
    url = "${httpConfig.getServerType}/files?uuid=${widget.uuid}&version=${httpConfig.version}";

    MyLog.d('StaffOneWebPage initState - URL: $url');
    MyLog.d('StaffOneWebPage initState - Token: ${httpConfig.token}');
    MyLog.d('StaffOneWebPage initState - Platform: ${defaultTargetPlatform.name}');

    ///在这里请求员工的档案详情
    _presenter?.getStaffOne();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: MyAppBar(
            centerTitle: '档案详情',
            centerSubTitle: _controller.userName.value,
            actionName: (_controller.showDelBut.value) ? '删除' : '',
            onPressed: () {
              ///如果是超级管理员 或者 超管 再删除
              if (_controller.showDelBut.value) {
                DialogManager.showConfirmDialog(
                  context: context,
                  title: '提示',
                  cancel: '取消',
                  confirm: '确定',
                  message: '是否删除该员工？',
                  onConfirm: () {
                    _presenter?.delCompanyStaff(widget.uuid ?? '');
                  },
                  onCancel: () {},
                );
              }
            },
          ),
          body: WillPopScope(
              child: XmWebView(
                webUrl: url, // 直接传递 URL 给 XmWebView
                onPageStarted: (url) {
                  MyLog.d('StaffOneWebPage onPageStarted: $url');
                },
                onPageFinished: (url) {
                  MyLog.d('StaffOneWebPage onPageFinished: $url');
                  _webViewReady = true;
                },
                onXmJsMsgCall: (XmJsMsgCall msgCall) {
                  var decode = json.decode(msgCall.message);
                  print('H5调用原声的回调name --> ${decode.toString()}');

                  var name = decode["name"];
                  var data = decode["data"];

                  ///回调token给用户
                  switch (name) {
                    case "getUserToken":
                      var token = httpConfig.token ?? "";
                      MyLog.d('StaffOneWebPage getUserToken called, token: $token, webViewReady: $_webViewReady');

                      // 确保 WebView 准备就绪后再执行回调
                      _executeGetUserTokenCallback(token);
                      break;
                    case "WebViewJavascriptBridge_editModule":
                      int num = data['num'];
                      BoostNavigator.instance.push('AddStaffPage', arguments: {
                        'jump_index': num,
                        'uuid': _controller.uuid.value,
                        'user_name': _controller.userName.value,
                        'channel': 3,
                      }).then((value) => controller.reload());
                      break;
                    case "goPolicyList":
                      break;
                  }
                },
                controllerSettingBack: (value) {
                  controller = value;
                  MyLog.d("StaffOneWebPage controllerSettingBack - URL: $url");
                  MyLog.d("StaffOneWebPage controllerSettingBack - Platform: ${defaultTargetPlatform.name}");
                  // 不在这里加载 URL，让 XmWebView 自己处理
                  // controller.loadRequest(Uri.parse(url ?? ""));
                },
                domainM2: false,
              ),
              onWillPop: () async {
                if (DialogManager.hasOpenDialogs()) {
                  DialogManager.dismissAllDialogs(context);
                  return false; // Prevent the app from popping the route
                } else {
                  return true; // Allow the app to pop the route
                }
              }),
          bottomNavigationBar: Container(
            color: Colors.white,
            height: 54,
            padding: const EdgeInsets.only(left: 10, right: 10),
            child: Column(
              children: [
                Gaps.line,
                Gaps.vGap8,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                        child: InkWell(
                      child: Container(
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Colours.base_primary, // Replace with your color
                          borderRadius: BorderRadius.circular(6.0),
                        ),
                        child: CommonUtils.getSimpleText('信用', 15, Colours.white, textAlign: TextAlign.center),
                      ),
                      onTap: () {
                        BoostNavigator.instance.push('creditInquiryPage', arguments: {
                          'uuid': _controller.uuid.value,
                          'user_name': _controller.userName.value,
                          'id_number': _controller.idNumber.value,
                          'type': '2', //类型 1入职查询 2员工查询
                        }).then((value) => _presenter?.getStaffOne());
                      },
                    )),
                    Gaps.hGap10,
                    Expanded(
                        child: InkWell(
                      child: Container(
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Colours.base_primary, // Replace with your color
                          borderRadius: BorderRadius.circular(6.0),
                        ),
                        child: CommonUtils.getSimpleText('合同', 15, Colours.white, textAlign: TextAlign.center),
                      ),
                      onTap: () {
                        BoostNavigator.instance.push('contractRecordPage', arguments: {
                          'uuid': _controller.uuid.value,
                          'user_name': _controller.userName.value,
                        }).then((value) => _presenter?.getStaffOne());
                      },
                    )),
                    Gaps.hGap10,
                    Expanded(
                        child: InkWell(
                      child: Container(
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Colours.base_primary, // Replace with your color
                          borderRadius: BorderRadius.circular(6.0),
                        ),
                        child: CommonUtils.getSimpleText('考勤', 15, Colours.white, textAlign: TextAlign.center),
                      ),
                      onTap: () {
                        BoostNavigator.instance.push('AttendanceOnePage', arguments: {
                          'user_uuid': _controller.uuid.value,
                          'user_name': _controller.userName.value,
                        }).then((value) => _presenter?.getStaffOne());
                      },
                    )),
                    Gaps.hGap10,
                    Expanded(
                        child: InkWell(
                      child: Container(
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Colours.base_primary, // Replace with your color
                          borderRadius: BorderRadius.circular(6.0),
                        ),
                        child: CommonUtils.getSimpleText('相册', 15, Colours.white, textAlign: TextAlign.center),
                      ),
                      onTap: () {
                        BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "gotoMyPhotos", "uuid": _controller.uuid.value, 'user_name': _controller.userName.value});
                      },
                    )),
                    Gaps.hGap10,
                    Expanded(
                        child: InkWell(
                      child: Container(
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Colours.base_primary, // Replace with your color
                          borderRadius: BorderRadius.circular(6.0),
                        ),
                        child: CommonUtils.getSimpleText(_controller.status.value == '1' ? '离职' : '再入职', 15, Colours.white, textAlign: TextAlign.center),
                      ),
                      onTap: () {
                        /// 1 在职 2离职
                        if (_controller.status.value == '1') {
                          ///快速去离职
                          BoostNavigator.instance.push('quickDeparkPage', arguments: {
                            'uuid': _controller.uuid.value,
                          }).then((value) => _presenter?.getStaffOne());
                        } else {
                          ///再次入职
                          BoostNavigator.instance.push('AddStaffPage', arguments: {
                            'uuid': _controller.uuid.value,
                            'again_entry': true, //再次入职
                          }).then((value) => _presenter?.getStaffOne());
                        }
                      },
                    )),
                  ],
                ),
              ],
            ),
          ),
        ));
  }

  /// 执行 getUserToken 回调，确保 WebView 准备就绪
  void _executeGetUserTokenCallback(String token) async {
    if (!_webViewReady) {
      MyLog.d('StaffOneWebPage WebView not ready, waiting...');
      // 等待 WebView 准备就绪，最多等待 5 秒
      int attempts = 0;
      while (!_webViewReady && attempts < 50) {
        await Future.delayed(const Duration(milliseconds: 100));
        attempts++;
      }
    }

    if (_webViewReady) {
      try {
        // 在 iOS 上添加额外延迟
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          await Future.delayed(const Duration(milliseconds: 200));
        }

        await controller.realController.runJavaScript(
          "window.WebViewJavascriptBridgegetUserTokenCallBack('$token')"
        );
        MyLog.d('StaffOneWebPage getUserToken callback executed successfully');
      } catch (error) {
        MyLog.e('StaffOneWebPage getUserToken callback failed: $error');
        // 如果第一次失败，再尝试一次
        try {
          await Future.delayed(const Duration(milliseconds: 500));
          await controller.realController.runJavaScript(
            "window.WebViewJavascriptBridgegetUserTokenCallBack('$token')"
          );
          MyLog.d('StaffOneWebPage getUserToken callback executed successfully on retry');
        } catch (retryError) {
          MyLog.e('StaffOneWebPage getUserToken callback failed on retry: $retryError');
        }
      }
    } else {
      MyLog.e('StaffOneWebPage WebView not ready after timeout');
    }
  }



  @override
  void dispose() {
    super.dispose();
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = StaffOnePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void requestPermissionData(PermissionEntity data) {}

  @override
  void updateStatus() {
    Toast.show('操作成功');
    Future.delayed(const Duration(seconds: 2), () {
      BoostNavigator.instance.pop();
    });
  }
}
