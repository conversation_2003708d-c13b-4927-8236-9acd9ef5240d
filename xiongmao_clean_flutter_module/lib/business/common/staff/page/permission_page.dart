import 'dart:collection';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/controller/permission_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/dimens.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../company/bean/department_company_entity.dart';
import '../controller/add_company_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/permiss_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/permission_persenter.dart';

/// 系统操作权限oage
class PermissionPage extends StatefulWidget {
  String channel;
  String? selectText;

  PermissionPage({Key? key, required this.channel, this.selectText});

  @override
  _MyState createState() => _MyState();
}

//集成分类 然后实现使用
class _MyState extends State<PermissionPage> with BasePageMixin<PermissionPage, PowerPresenter<dynamic>> implements PermissionIView {
  final PermissionController _controller = PermissionController();
  List<String> list = [];

  late PermissionPresenter _presenter;

  @override
  void initState() {
    super.initState();
    _presenter.requestPermission(widget.channel);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: "选择系统操作权限",
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: SafeArea(
        child: Obx(() {
          return ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
              shrinkWrap: true,
              itemCount: _controller.permissionEntity.value.length,
              itemBuilder: (context, index) {
                var value = _controller.permissionEntity.value[index];
                return InkWell(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: BrnShadowCard(
                      blurRadius: 0,
                      color: Colours.white,
                      circular: 10,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                        child: Row(
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Gaps.vGap6,
                                CommonUtils.getSimpleText(value.name, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                                Gaps.vGap6,
                                CommonUtils.getSimpleText(value.role_desc, 14, Colours.base_primary_text_title),
                                Gaps.vGap6,
                              ],
                            )),
                            Visibility(
                              visible: widget.selectText?.contains(value.name ?? "") ?? false,
                              child: const LoadAssetImage(
                                "icon_base_selected",
                                width: 30,
                                height: 30,
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  onTap: () {
                    BoostNavigator.instance.pop(value);
                  },
                );
              });
        }),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = PermissionPresenter();
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void requestPermissionData(PermissionEntity data) {
    _controller.updatePermissionData(data.list ?? []);
  }
}
