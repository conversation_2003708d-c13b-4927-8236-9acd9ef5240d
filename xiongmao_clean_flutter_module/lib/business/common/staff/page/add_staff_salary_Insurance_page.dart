import 'dart:collection';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/create_staff_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../company/bean/department_company_entity.dart';
import '../bean/entry_condition_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/add_company_controller.dart';
import '../controller/add_staff_main_controller.dart';
import '../controller/add_staff_salary_Info_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/add_staff_main_iview.dart';
import '../iview/add_staff_salary_Info_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/add_staff_main_persenter.dart';
import '../persenter/add_staff_salary_Info_persenter.dart';
import '../widght/flutter_steps.dart';
import '../widght/steps.dart';

/// 薪水
class AddStaffSalaryInsurancePage extends StatefulWidget {
  String? uuid = "";
  String? applicationNo = "";
  int channel;

  final Function(String) onNext;
  final Function onPrevious;
  EntryConditionEntity? entryCondition;

  AddStaffSalaryInsurancePage({
    Key? key,
    this.uuid = "",
    this.applicationNo = "",
    required this.entryCondition,
    required this.channel,
    required this.onNext,
    required this.onPrevious,
  });

  @override
  _AddStaffSalaryInsurancePageState createState() => _AddStaffSalaryInsurancePageState();
}

class _AddStaffSalaryInsurancePageState extends State<AddStaffSalaryInsurancePage> with BasePageMixin<AddStaffSalaryInsurancePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin implements AddStaffSalaryInfoIView {
  AddStaffSalaryInfoPresenter? _presenter;
  final AddStaffSalaryInfoController _controller = AddStaffSalaryInfoController();

  @override
  void initState() {
    super.initState();
    _controller.uuid.value = widget.uuid ?? '';
    _controller.applicationNo.value = widget.applicationNo ?? '';

    ///请求详情
    var isDraft = true;
    if (widget.entryCondition != null && !TextUtil.isEmpty(widget.entryCondition!.uuid)) {
      if (widget.entryCondition!.beforeEntry) {
        ///档案详情 延用以前的详情
        isDraft = false;
      } else {
        ///草稿详情
        isDraft = true;
      }

      ///请求详情
      _presenter?.getStaffOne(true, isDraft, widget.entryCondition!.uuid);
    } else {
      isDraft = widget.channel == 3 ? false : true;

      ///请求详情
      _presenter?.getStaffOne(false, isDraft, _controller.uuid.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      body: SingleChildScrollView(
        child: Obx(() => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 10),
                  width: double.infinity,
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        child: CommonUtils.getSimpleText('薪酬待遇', 16, Colours.base_primary_text_title),
                      ),
                      BrnTextInputFormItem(
                        isRequire: true,
                        title: "基础薪资",
                        hint: "请输入",
                        controller: _controller.salaryController,
                      ),
                    ],
                  ),
                ),
                Gaps.vGap10,
                Container(
                  width: double.infinity,
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        child: CommonUtils.getSimpleText('保险保障', 16, Colours.base_primary_text_title),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 18, right: 16, bottom: 10, top: 10),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  top: 10,
                                ),
                                child: CommonUtils.getSimpleText('参保方式', 15, Colours.base_primary_text_title),
                              ),
                            ),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  key: ValueKey(_controller.insureIndex.value),
                                  _controller.insureList.value,
                                  multiSelect: false,
                                  crossAxisCount: 3,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: [_controller.insureIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 3,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    _controller.insureIndex.value = (indexs[0]).toInt();
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Visibility(
                        visible: _controller.insureIndex.value == 0,
                        child: Padding(
                          padding: const EdgeInsets.only(left: 16, right: 16, bottom: 14, top: 14),
                          child: Row(
                            children: [
                              CommonUtils.getSimpleText('参保方案', 15, Colours.base_primary_text_caption),
                              Gaps.hGap10,
                              Expanded(
                                child: CommonUtils.getSimpleText(_controller.insureDesc.value, 15, Colours.base_primary_text_caption, textAlign: TextAlign.right),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Visibility(
                  visible: _controller.insureIndex.value == 0,
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: 18,
                      right: 18,
                      top: 10,
                    ),
                    child: CommonUtils.getSimpleText('入职审批通过后自动上保险。', 12, Colours.base_primary_text_body),
                  ),
                )
              ],
            )),
      ),
      bottomNavigationBar: Container(
        height: 60,
        color: Colors.white,
        child: Column(
          children: [
            Gaps.line,
            Row(
              children: [
                Visibility(
                  visible: (widget.channel != 3 && widget.channel != 4),
                  child: Expanded(
                      child: InkWell(
                    child: Container(
                      alignment: Alignment.center,
                      width: double.infinity,
                      height: 44,
                      margin: const EdgeInsets.only(
                        top: 8,
                        left: 10,
                      ),
                      decoration: BoxDecoration(
                        color: Colours.base_primary_un_select,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: CommonUtils.getSimpleText('上一步', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    ),
                    onTap: () {
                      widget.onPrevious();
                    },
                  )),
                ),
                Gaps.hGap10,
                Expanded(
                    flex: 2,
                    child: InkWell(
                      child: Container(
                        alignment: Alignment.center,
                        width: double.infinity,
                        height: 44,
                        margin: const EdgeInsets.only(
                          top: 8,
                          right: 10,
                        ),
                        decoration: BoxDecoration(
                          color: Colours.base_primary,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: CommonUtils.getSimpleText((widget.channel == 3 || widget.channel == 4) ? '提交' : '下一步', 16, Colours.white, fontWeight: FontWeight.bold),
                      ),
                      onTap: () {
                        if (TextUtil.isEmpty(_controller.salaryController.text)) {
                          Toast.show('请输入基础薪资');
                          return;
                        }
                        HashMap<String, String> map = HashMap();
                        map['op_type'] = 'salary';
                        map['uuid'] = _controller.uuid.value;
                        map['salary'] = _controller.salaryController.text;

                        ///参保类型 1不参保 2社保 3商业保险
                        map['insurance_type'] = _controller.insureIndex.value == 0
                            ? '3'
                            : _controller.insureIndex.value == 1
                                ? '2'
                                : '1';
                        _presenter?.updateStaffInfo(widget.channel == 3 ? false : true, map);
                      },
                    )),
              ],
            )
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true; // 保持活跃

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddStaffSalaryInfoPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void onUpdate(CreateStaffEntity data) {
    widget.onNext(data.uuid ?? '');
  }
}
