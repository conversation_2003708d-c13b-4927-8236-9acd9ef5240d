import 'dart:collection';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/job_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/create_staff_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../company/bean/department_company_contract_entity.dart';
import '../../company/bean/department_company_entity.dart';
import '../../project/bean/attendance_manager_rules_entity.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../project/item/project_manager_item_listview.dart';
import '../bean/entry_condition_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/add_company_controller.dart';
import '../controller/add_staff_main_controller.dart';
import '../controller/add_staff_position_arrangement_Info_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/add_staff_main_iview.dart';
import '../iview/add_staff_position_arrangement_Info_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/add_staff_main_persenter.dart';
import '../persenter/add_staff_position_arrangement_Info_persenter.dart';
import '../widght/flutter_steps.dart';
import '../widght/steps.dart';

/// 添加任职安排
class AddStaffPositionArrangementPage extends StatefulWidget {
  String? uuid = "";

  String? applicationNo = "";
  int channel;

  final Function(String) onNext;
  final Function onPrevious;
  EntryConditionEntity? entryCondition;

  AddStaffPositionArrangementPage({
    Key? key,
    this.uuid = "",
    this.applicationNo = "",
    required this.entryCondition,
    required this.channel,
    required this.onNext,
    required this.onPrevious,
  });

  @override
  _AddStaffPositionArrangementPageState createState() => _AddStaffPositionArrangementPageState();
}

class _AddStaffPositionArrangementPageState extends State<AddStaffPositionArrangementPage> with BasePageMixin<AddStaffPositionArrangementPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin implements AddStaffPositionArrangementInfoIView {
  AddStaffPositionArrangementInfoPresenter? _presenter;
  final AddStaffPositionArrangementInfoController _controller = AddStaffPositionArrangementInfoController();

  @override
  void initState() {
    super.initState();
    _controller.channel.value = widget.channel;
    _controller.uuid.value = widget.uuid ?? '';
    _controller.applicationNo.value = widget.applicationNo ?? '';

    ///获取是否允许补录之前的信息
    _controller.isEntryBeforeToday.value = SpUtil.getBool(Constant.IS_ENTRY_BEFORE_TODAY) ?? false;

    ///请求详情
    var isDraft = true;
    if (widget.entryCondition != null && !TextUtil.isEmpty(widget.entryCondition!.uuid)) {
      if (widget.entryCondition!.beforeEntry) {
        ///档案详情 延用以前的详情
        isDraft = false;
      } else {
        ///草稿详情
        isDraft = true;
      }

      ///请求详情
      _presenter?.getStaffOne(true, isDraft, widget.entryCondition!.uuid);
    } else {
      isDraft = widget.channel == 3 ? false : true;

      ///请求详情
      _presenter?.getStaffOne(false, isDraft, _controller.uuid.value);
    }
    _presenter?.getHeadOfficeProject();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => SingleChildScrollView(
            child: Column(
              children: [
                Gaps.vGap10,
                BrnTextSelectFormItem(
                  title: "入职日期",
                  isRequire: true,
                  value: _controller.entryDate.value,
                  onTap: () {
                    FocusScope.of(context).requestFocus(FocusNode()); // 失去焦点
                    BrnDatePicker.showDatePicker(
                      themeData: BrnPickerConfig(
                        pickerHeight: 300,
                      ),
                      context,
                      initialDateTime: DateTime.tryParse(_controller.entryDate.value),
                      minDateTime: !_controller.isEntryBeforeToday.value ? DateTime.now().subtract(const Duration(days: 365 * 20)) : DateTime.now(),
                      pickerMode: BrnDateTimePickerMode.datetime,
                      dateFormat: 'yyyy年,MMMM月,dd日',
                      onConfirm: (dateTime, list) {
                        // 确保 dateTime 不为 null
                        _controller.entryDate.value = DateUtil.formatDate(dateTime, format: 'yyyy-MM-dd');
                      },
                    );
                  },
                ),
                Gaps.line,
                Visibility(
                  visible: httpConfig.role_id != HttpConfig.ROLE_LEADER_ID || httpConfig.role_id != HttpConfig.ROLE_PROJECT_OWNER_ID,
                  child: Container(
                    color: Colors.white,
                    padding: const EdgeInsets.only(right: 16),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 1,
                          child: BrnBaseTitle(
                            isRequire: true,
                            title: "入职到",
                          ),
                        ),
                        Expanded(
                            flex: 2,
                            child: SelectTabWidget(
                              key: ValueKey(_controller.workIndex.value),
                              _controller.workList.value,
                              multiSelect: false,
                              crossAxisCount: 2,
                              hideMore: false,
                              paddingBottom: 0,
                              paddingTop: 4,
                              tabFontSize: 15,
                              defaultSelectedIndex: [_controller.workIndex.value],
                              lastIsAddOne: false,
                              selectedColor: Colours.base_primary,
                              bgSelectedColor: Colours.base_primary_select,
                              bgUnSelectedColor: Colours.base_primary_un_select,
                              childAspectRatio: 6 / 2,
                              itemClickCallback: (List<int> indexs) {
                                LogUtil.e("indexs = $indexs");
                                FocusScope.of(context).requestFocus(FocusNode()); // 失去焦点
                                _controller.workIndex.value = (indexs[0]).toInt();

                                ///切换的时候，要清空原先的选项
                                _controller.cleanSelect();

                                ///根据不同的选择，做处理 0是总部 1是项目
                                if (_controller.workIndex.value == 0) {
                                  _controller.projectUuid.value = _controller.headOfficeManger.value.uuid ?? '';
                                  _controller.projectName.value = _controller.headOfficeManger.value.projectShortName ?? '';

                                  ///请求岗位跟考勤规则
                                  _presenter?.getCurrentProjectJobs();
                                  _presenter?.getCurrentProjectAttendanceRules();
                                } else {
                                  ///去拿新的项目信息
                                  _presenter?.getProjectManagerList(null);
                                }
                              },
                            )),
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: _controller.workIndex.value == 1,
                  child: Column(
                    children: [
                      Gaps.line,
                      BrnTextSelectFormItem(
                        isRequire: true,
                        title: "项目",
                        value: _controller.projectName.value,
                        onTap: () {
                          FocusScope.of(context).requestFocus(FocusNode()); // 失去焦点
                          BoostNavigator.instance.push('ProjectManagerPage', arguments: {
                            'choose_uuid': _controller.projectUuid.value,
                            'isSelected': true,
                          }).then((value) {
                            if (value is ProjectManagerList) {
                              _controller.projectUuid.value = value.uuid ?? '';
                              _controller.projectName.value = (TextUtil.isEmpty(value.projectShortName)) ? value.projectName ?? '' : value.projectShortName ?? '';

                              ///选择了项目后 拿到projectUuid 去重新去拉岗位跟考勤规则
                              _presenter?.getCurrentProjectJobs();
                              _presenter?.getCurrentProjectAttendanceRules();
                            }
                          });
                        },
                      ),
                      Gaps.line,
                      BrnTextSelectFormItem(
                        isRequire: true,
                        title: "岗位",
                        value: _controller.jobName.value,
                        onTap: () {
                          FocusScope.of(context).requestFocus(FocusNode()); // 失去焦点
                          if (TextUtil.isEmpty(_controller.projectUuid.value)) {
                            Toast.show('请先选择项目');
                            return;
                          }
                          if (_controller.jobManagerList.isEmpty) {
                            _presenter?.getCurrentProjectJobs();
                            return;
                          }
                          showJobDialog();
                        },
                      ),
                    ],
                  ),
                ),
                Visibility(
                    visible: _controller.workIndex.value == 0,
                    child: Column(
                      children: [
                        Gaps.line,
                        BrnTextSelectFormItem(
                          isRequire: true,
                          title: "所属部门",
                          value: _controller.departmentName.value,
                          onTap: () {
                            FocusScope.of(context).requestFocus(FocusNode()); // 失去焦点
                            BoostNavigator.instance.push(
                              'companyManagerStaffBelongPage',
                              arguments: {'uuid': _controller.departmentUuid.value},
                            ).then((value) {
                              if (value is DepartmentCompanyList) {
                                _controller.departmentUuid.value = value.uuid ?? '';
                                _controller.departmentName.value = value.departmentName ?? '';
                              }
                            });
                          },
                        ),
                        Gaps.line,
                        BrnTextInputFormItem(
                          isRequire: true,
                          title: "职位描述",
                          hint: "请输入",
                          controller: _controller.jobDescController,
                        ),
                      ],
                    )),
                Gaps.line,
                BrnTextSelectFormItem(
                  isRequire: true,
                  title: "考勤规则",
                  subTitle: '该员工的出勤打卡规则，例如早班、中班、晚班等。',
                  value: _controller.attendanceRuleName.value,
                  onTap: () {
                    if (TextUtil.isEmpty(_controller.projectUuid.value)) {
                      Toast.show('请先选择项目');
                      return;
                    }
                    BoostNavigator.instance.push('ProjectAttendanceManagerRulesPage', arguments: {
                      "project_uuid": _controller.projectUuid.value,
                      "project_name": _controller.projectName.value,
                      "choose_uuid": _controller.attendanceRuleUuid.value,
                      "isSelected": true,
                    }).then((value) {
                      if (value is AttendanceManagerRulesList) {
                        _controller.attendanceRuleUuid.value = value.uuid ?? '';
                        _controller.attendanceRuleName.value = value.groupName ?? '';
                      }
                    });
                  },
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  isRequire: true,
                  isEdit: _controller.roleUuid.value != HttpConfig.ROLE_SUPER_MANGER_ID,
                  title: "角色",
                  value: _controller.roleName.value,
                  onTap: () {
                    BoostNavigator.instance.push('permissionPage', arguments: {"channel": '${_controller.workIndex.value + 1}', "selectText": _controller.roleName.value}).then((value) {
                      if (value is PermissionData) {
                        _controller.roleUuid.value = value.id ?? '';
                        _controller.roleName.value = value.name ?? '';
                      }
                    });
                  },
                ),
                Gaps.line,
                Visibility(
                  visible: _controller.workIndex.value == 0,
                  child: BrnTextSelectFormItem(
                    title: "合同公司",
                    value: _controller.companyName.value,
                    onTap: () {
                      BoostNavigator.instance.push(
                        'companyChoiceContractPage',
                        arguments: {'uuid': _controller.companyUuid.value},
                      ).then((value) {
                        if (value is DepartmentCompanyContractList) {
                          _controller.companyUuid.value = value.uuid ?? '';
                          _controller.companyName.value = value.companyName ?? '';
                        }
                      });
                    },
                  ),
                ),
              ],
            ),
          )),
      bottomNavigationBar: Container(
        height: 60,
        color: Colors.white,
        child: Column(
          children: [
            Gaps.line,
            Row(
              children: [
                Visibility(
                  visible: (widget.channel != 3 && widget.channel != 4),
                  child: Expanded(
                      child: InkWell(
                    child: Container(
                      alignment: Alignment.center,
                      width: double.infinity,
                      height: 44,
                      margin: const EdgeInsets.only(
                        top: 8,
                        left: 10,
                      ),
                      decoration: BoxDecoration(
                        color: Colours.base_primary_un_select,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: CommonUtils.getSimpleText('上一步', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    ),
                    onTap: () {
                      widget.onPrevious();
                    },
                  )),
                ),
                Gaps.hGap10,
                Expanded(
                    flex: 2,
                    child: InkWell(
                      child: Container(
                        alignment: Alignment.center,
                        width: double.infinity,
                        height: 44,
                        margin: const EdgeInsets.only(
                          top: 8,
                          right: 10,
                        ),
                        decoration: BoxDecoration(
                          color: Colours.base_primary,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: CommonUtils.getSimpleText((widget.channel == 3 || widget.channel == 4) ? '提交' : '下一步', 16, Colours.white, fontWeight: FontWeight.bold),
                      ),
                      onTap: () {
                        if (TextUtil.isEmpty(_controller.entryDate.value)) {
                          Toast.show('请选择入职日期');
                          return;
                        }
                        if (_controller.workIndex.value == 0) {
                          if (TextUtil.isEmpty(_controller.departmentUuid.value)) {
                            Toast.show('请选择所属部门');
                            return;
                          }
                          if (TextUtil.isEmpty(_controller.jobDescController.text)) {
                            Toast.show('请输入职位描述');
                            return;
                          }
                        } else {
                          if (TextUtil.isEmpty(_controller.projectUuid.value)) {
                            Toast.show('请选择项目');
                            return;
                          }
                          if (TextUtil.isEmpty(_controller.jobUuid.value)) {
                            Toast.show('请选择岗位');
                            return;
                          }
                        }

                        if (TextUtil.isEmpty(_controller.attendanceRuleUuid.value)) {
                          Toast.show('请选择考勤规则');
                          return;
                        }
                        if (TextUtil.isEmpty(_controller.roleUuid.value)) {
                          Toast.show('请选择角色');
                          return;
                        }
                        HashMap<String, String> hashMap = HashMap();
                        hashMap['op_type'] = 'work_info';
                        hashMap['uuid'] = _controller.uuid.value;

                        ///是否总部 0未知 1是 2否
                        hashMap["is_head_office"] = '${_controller.workIndex.value + 1}';

                        /// 0是总部 1是项目
                        if (_controller.workIndex.value == 0) {
                          hashMap['contract_company_uuid'] = _controller.companyUuid.value;
                          hashMap['department_uuid'] = _controller.departmentUuid.value;
                          hashMap['job_name'] = _controller.jobDescController.text;
                        } else {
                          hashMap["job_uuid"] = _controller.jobUuid.value;
                          hashMap['job_name'] = _controller.jobName.value;
                        }
                        hashMap["project_uuid"] = _controller.projectUuid.value;
                        if (!TextUtil.isEmpty(_controller.attendanceRuleUuid.value)) {
                          hashMap["group_uuid"] = _controller.attendanceRuleUuid.value;
                        }

                        ///入职日期
                        hashMap["work_start_time"] = _controller.entryDate.value;

                        ///角色
                        hashMap["role_id"] = _controller.roleUuid.value;

                        _presenter?.updateStaffInfo(widget.channel == 3 ? false : true, hashMap);
                      },
                    )),
              ],
            )
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true; // 保持活跃

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddStaffPositionArrangementInfoPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void onUpdate(CreateStaffEntity data) {
    widget.onNext(data.uuid ?? '');
  }

  @override
  void getCurrentProjectJobs(JobManagerEntity data) {
    ///获取当前的所有岗位
    _controller.jobManagerList.clear();
    _controller.jobManagerList = data.list ?? [];
    if (TextUtil.isEmpty(_controller.jobName.value)) {
      ///如果是空的，那么从list中取，并且反选
      if (data.list != null && data.list!.isNotEmpty) {
        for (JobManagerList jobManager in data.list!) {
          if (!TextUtil.isEmpty(jobManager.jobName) && jobManager.jobName!.contains('保洁')) {
            _controller.jobName.value = jobManager.jobName!;
            _controller.jobUuid.value = jobManager.uuid!;
            return;
          }
        }
      }
    } else {
      ///弹窗让用户自选岗位
      showJobDialog();
    }
  }

  ///显示工作的Dialog
  void showJobDialog() {
    List<String> jobs = _controller.jobManagerList.map((e) => e.jobName).where((name) => name != null).cast<String>().toList();

    SingleColumnDataPickerView.showSingleColumnDataPicker(context, '请选择岗位', jobs, _controller.jobIndex.value, (index, selectedText) {
      print('当前选择的下标：$index');
      print('当前选择的文本内容：$selectedText');
      _controller.jobIndex.value = index;
      _controller.jobName.value = selectedText;
      _controller.jobUuid.value = _controller.jobManagerList[index].uuid ?? '';
    });
  }
}
