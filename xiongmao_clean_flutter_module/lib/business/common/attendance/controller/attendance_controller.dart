import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/attendance_data_entity.dart';
import '../bean/project_classes_data_entity.dart';

class AttendanceController extends GetxController {
  ///记录报告当前的类型
  var reportType = 0;

  var projectName = "全部项目".obs;
  var projectUuid = "".obs;

  //是否跳转考勤
  var gotoAttendance = false.obs;

  var projectExportName = "".obs;
  var projectExportUuid = "".obs;

  var projectExportDate = "".obs;

  ///导出类型
  var projectExportType = "0".obs;

  ///月报的日期
  var searchMonth = "".obs;

  ///日报的日期
  var searchDay = "".obs;

  ///状态
  var status = '0'.obs;

  var data = AttendanceDataEntity().obs;

  var list = <AttendanceDataList>[].obs;

  var totalNumber = "0".obs;

  void initMyList(int total, List<AttendanceDataList> value) {
    totalNumber.value = total.toString();
    list.value = value.toList();
  }

  void updateMyList(List<AttendanceDataList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///开票类型
  var exportTypeList = [
    BaseChooseString("考勤结果"),
    BaseChooseString("有效打卡记录"),
    BaseChooseString("原始打卡记录"),
  ].obs;

  ///*********************************获取当前的导出月份**************************************

  final List<String> months = ["01月", "02月", "03月", "04月", "05月", "06月", "07月", "08月", "09月", "10月", "11月", "12月"];

  String getMonthName(int monthNumber) {
    return months[monthNumber - 1];
  }

  List<String> getMonthsInRange(DateTime start, DateTime end) {
    List<String> result = [];
    for (int year = start.year; year <= end.year; year++) {
      int startMonth = year == start.year ? start.month : 1;
      int endMonth = year == end.year ? end.month : 12;
      for (int month = startMonth; month <= endMonth; month++) {
        result.add('$year年${getMonthName(month)}');
      }
    }
    return result;
  }

  int getCurrentMonthIndex(List<String> monthsInRange) {
    DateTime now = DateTime.now();
    String currentMonthStr = '${now.year}年${getMonthName(now.month)}';
    return monthsInRange.indexOf(currentMonthStr);
  }

  ///*********************************获取当前的导出月份**************************************
}
