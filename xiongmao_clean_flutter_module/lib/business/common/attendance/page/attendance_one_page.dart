import 'dart:async';
import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/my_attendance_day_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/my_attendance_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/dialog_manager.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_search_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/loading_util.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../attendance/bean/project_classes_data_entity.dart';
import '../../attendance/widget/custom_attendance_classes_view.dart';
import '../../attendance/widget/custom_attendance_holiday_view.dart';
import '../../attendance/widget/custom_attendance_result_view.dart';
import '../bean/attendance_calendar_tag.dart';
import '../bean/attendance_update.dart';
import '../controller/attendance_controller.dart';
import '../controller/attendance_one_controller.dart';
import '../item/attendance_holiday_record_list_Item.dart';
import '../item/attendance_one_day_overtime_list_Item.dart';
import '../item/attendance_one_top_list_Item.dart';
import '../item/attendance_operatelog_record_list_Item.dart';
import '../item/attendance_record_list_Item.dart';
import '../item/attendance_work_overtime_record_list_Item.dart';
import '../iview/attendance_iview.dart';
import '../iview/attendance_one_iview.dart';
import '../persenter/attendance_one_persenter.dart';
import '../persenter/attendance_persenter.dart';
import '../widget/custom_calendar.dart';

/// 考勤详情页
class AttendanceOnePage extends StatefulWidget {
  String userUuid;
  String userName;
  String projectUuid;
  String searchDayDate;

  AttendanceOnePage({super.key, required this.userUuid, required this.userName, required this.projectUuid, required this.searchDayDate});

  @override
  _AttendanceOnePageState createState() => _AttendanceOnePageState();
}

class _AttendanceOnePageState extends State<AttendanceOnePage> with BasePageMixin<AttendanceOnePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<AttendanceOnePage> implements AttendanceOneIView {
  AttendanceOnePresenter? _presenter;

  final AttendanceOneController _controller = AttendanceOneController();

  ScrollController _scrollController = ScrollController();

  DateTime _currentNowDate = DateTime.now();

  void _prevMonth() {
    setState(() {
      _currentNowDate = DateTime(_currentNowDate.year, _currentNowDate.month - 1);
      requestOne();
    });
  }

  void _nextMonth() {
    setState(() {
      _currentNowDate = DateTime(_currentNowDate.year, _currentNowDate.month + 1);
      requestOne();
    });
  }

  /// 判断是否可以显示添加的按钮
  bool _shouldShowAddWidget() {
    ///如果当前的角色是保洁员，那么就不显示
    if (httpConfig.role_id == HttpConfig.ROLE_CLEAN_ID) return false;

    ///如果当前是自己的话也不能更改 说明是自己
    if (httpConfig.user_uuid == widget.userUuid) return false;

    return true;
  }

  /// 判断是否显示打卡结果
  bool _shouldShowUpdateClassWidget() {
    ///如果当前是自己的话也不能更改 说明是自己
    if (httpConfig.user_uuid == widget.userUuid) return false;

    ///如果考勤列表的值大于1 就不显示这个外部的了
    if (_controller.myAttendanceDay != null && _controller.myAttendanceDay!.list != null && _controller.myAttendanceDay!.list!.length > 1) {
      return false;
    }

    return true;
  }

  /// 是否可以改班次
  bool _shouldShowUpdateClassViewWidget() {
    ///如果当前是自己的话也不能更改 说明是自己
    print('用户的id对比 ${httpConfig.user_uuid} - ${_controller.userUuid.value}');
    if (httpConfig.user_uuid == _controller.userUuid.value) return false;

    print('用户的身份对比 ${httpConfig.role_id}');

    ///如果当前角色是保洁员，那么就不显示
    if (httpConfig.role_id == HttpConfig.ROLE_CLEAN_ID) return false;

    return true;
  }

  @override
  void initState() {
    super.initState();
    _controller.userUuid.value = widget.userUuid;

    ///判断外部传递的日期 外部的日期给先在的date对象
    if (!TextUtil.isEmpty(widget.searchDayDate)) {
      _currentNowDate = DateTime.parse(widget.searchDayDate);
    }

    requestOne();
  }

  Future<dynamic> requestOne() async {
    ///初始默认的数据
    _controller.getAttendanceStat(null);
    _controller.currentDate.value = '${_currentNowDate.year}-${CommonUtils.formatToTwoDigits(_currentNowDate.month)}';
    _controller.currentDateDay.value = '${_currentNowDate.year}-${CommonUtils.formatToTwoDigits(_currentNowDate.month)}-${CommonUtils.formatToTwoDigits(_currentNowDate.day)}';

    ///获取当前的用户的考勤
    _presenter?.getMyAttendanceOne();
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.white,
      appBar: MyAppBar(
        centerTitle: '考勤详情',
        centerSubTitle: widget.userName,
      ),
      body: WillPopScope(
          child: RefreshIndicator(
            onRefresh: requestOne,
            child: Obx(() => SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        color: Colours.base_primary_bg_page,
                        height: 10,
                      ),
                      Container(
                        color: Colors.white,
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 10),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  InkWell(
                                    child: Transform.rotate(
                                      angle: 66,
                                      child: LoadImage(
                                        'common/icon_left_attendance',
                                        width: 30,
                                        height: 30,
                                      ),
                                    ),
                                    onTap: () {
                                      _prevMonth();
                                      print('切换了下月${_currentNowDate.year}-${_currentNowDate.month}');
                                    },
                                  ),
                                  Expanded(child: CommonUtils.getSimpleText(_controller.currentDate.value, 16, Colours.base_primary_text_title, textAlign: TextAlign.center)),
                                  InkWell(
                                    child: LoadImage(
                                      'common/icon_left_attendance',
                                      width: 30,
                                      height: 30,
                                    ),
                                    onTap: () {
                                      _nextMonth();
                                      print('切换了下月${_currentNowDate.year}-${_currentNowDate.month}');
                                    },
                                  ),
                                ],
                              ),
                            ),
                            Gaps.line,
                            GridView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              padding: const EdgeInsets.only(left: 10, right: 10, top: 4, bottom: 4),
                              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 5,
                                crossAxisSpacing: 16,
                                mainAxisSpacing: 0,
                              ),
                              shrinkWrap: true,
                              itemCount: _controller.attendanceTopList.length,
                              itemBuilder: (context, index) {
                                return AttendanceOneTopListItem(
                                  data: _controller.attendanceTopList[index],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      Container(
                        color: Colours.base_primary_bg_page,
                        height: 10,
                      ),

                      ///这里要显示的日历组件
                      CustomCalendar(
                        schemeCount: _controller.maxSchemeCount.value,
                        initialDate: _currentNowDate,
                        initialTags: _controller.tags,
                        onDateSelected: (date) {
                          print('自己定义的点击了谁${date}');
                          _controller.currentDateDay.value = '${date.year}-${CommonUtils.formatToTwoDigits(date.month)}-${CommonUtils.formatToTwoDigits(date.day)}';
                          _currentNowDate = date;
                          _presenter?.getMyAttendanceDayOne();
                        },
                      ),

                      Gaps.line,

                      ///这里是记录等数据
                      ///无排班的时候走这里
                      Visibility(
                          visible: _controller.myAttendanceDay?.dayType == '3',
                          child: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left: 16, right: 16, top: 30, bottom: 30),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CommonUtils.getSimpleText('该日无需考勤', 20, Colours.base_primary_text_title, textAlign: TextAlign.center),
                                  ],
                                ),
                              ),
                            ],
                          )),

                      ///有排版的时候显示
                      Visibility(
                          visible: _controller.myAttendanceDay?.dayType != '3',
                          child: Column(
                            children: [
                              ///考勤记录
                              Padding(
                                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                                child: Row(
                                  children: [
                                    Expanded(child: CommonUtils.getSimpleText((!TextUtil.isEmpty(_controller.myAttendanceDay?.className)) ? _controller.myAttendanceDay?.className : '考勤记录', 18, Colours.base_primary_text_title, fontWeight: FontWeight.bold, overflow: TextOverflow.ellipsis)),
                                    Row(
                                      children: [
                                        Visibility(
                                          visible: _shouldShowUpdateClassViewWidget(),
                                          child: InkWell(
                                            child: Container(
                                              decoration: BoxDecoration(
                                                border: Border.all(color: Colours.base_primary, width: 0.5),
                                                borderRadius: BorderRadius.circular(4.0),
                                              ),
                                              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
                                              child: CommonUtils.getSimpleText('改班次', 14, Colours.base_primary),
                                            ),
                                            onTap: () {
                                              _presenter?.getClassesManager(_controller.myAttendanceOne?.userName ?? '', _controller.currentDateDay.value, _controller.myAttendanceOne?.projectUuid ?? '');
                                            },
                                          ),
                                        ),
                                        Visibility(
                                          visible: _shouldShowUpdateClassWidget(),
                                          child: InkWell(
                                            splashColor: Colors.transparent, // 取消水波纹颜色
                                            highlightColor: Colors.transparent, // 取消高亮背景色
                                            child: Container(
                                              decoration: BoxDecoration(
                                                color: Colours.base_primary,
                                                borderRadius: BorderRadius.circular(4),
                                              ),
                                              margin: const EdgeInsets.only(left: 10),
                                              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                                              child: CommonUtils.getSimpleText('改打卡结果', 14, Colours.white),
                                            ),
                                            onTap: () {
                                              if ('2' == _controller.myAttendanceDay?.dayType) {
                                                showHolidayDialog();
                                              } else {
                                                showAttendanceDialog(_controller.myAttendanceDay!.list![0].uuid ?? '');
                                              }
                                            },
                                          ),
                                        )
                                      ],
                                    ),
                                  ],
                                ),
                              ),

                              ///考勤列表
                              Container(
                                margin: const EdgeInsets.only(left: 16, right: 16, bottom: 10),
                                padding: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.list == null || _controller.myAttendanceDay!.list!.isEmpty) ? const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10) : const EdgeInsets.only(top: 0, bottom: 0, left: 0, right: 0),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.list == null || _controller.myAttendanceDay!.list!.isEmpty) ? Colours.base_primary_bg_page : Colours.transparent,
                                  borderRadius: BorderRadius.circular((_controller.myAttendanceDay == null || _controller.myAttendanceDay?.list == null || _controller.myAttendanceDay!.list!.isEmpty) ? 0 : 4.0),
                                ),
                                child: Column(
                                  children: [
                                    Visibility(
                                      visible: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.list == null || _controller.myAttendanceDay!.list!.isEmpty),
                                      child: CommonUtils.getSimpleText((_controller.myAttendanceDay?.dayType == '2') ? '休息日无需打卡' : '暂无考勤记录', 15, Colours.base_primary_text_title, textAlign: TextAlign.center),
                                    ),

                                    ///这里显示考勤的记录
                                    Visibility(
                                      visible: (_controller.myAttendanceDay != null && _controller.myAttendanceDay?.list != null && _controller.myAttendanceDay!.list!.isNotEmpty),
                                      child: ListView.builder(
                                          physics: const NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount: _controller.myAttendanceDay?.list?.length,
                                          itemBuilder: (_, index) {
                                            return AttendanceRecordListItem(
                                              data: _controller.myAttendanceDay!.list![index],
                                              showBut: _shouldShowAddWidget(),
                                              position: index,
                                              onClick: (position) {
                                                print('$position');

                                                ///这里是修改打卡结果 如果是休息日
                                                switch (position) {
                                                  case 1:
                                                    if ('2' == _controller.myAttendanceDay?.dayType) {
                                                      showHolidayDialog();
                                                    } else {
                                                      showAttendanceDialog(_controller.myAttendanceDay!.list![index].uuid ?? '');
                                                    }
                                                    break;
                                                  case 2:
                                                    showLookAttendanceDialog(true, _controller.myAttendanceDay!.list![index]);
                                                    break;
                                                  case 3:
                                                    showLookAttendanceDialog(false, _controller.myAttendanceDay!.list![index]);
                                                    break;
                                                }
                                              },
                                              showTitle: _controller.myAttendanceDay!.list!.length != 1,
                                            );
                                          }),
                                    ),
                                  ],
                                ),
                              ),

                              ///加班记录
                              Padding(
                                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Expanded(child: CommonUtils.getSimpleText('加班记录', 18, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                                    Visibility(
                                      visible: _shouldShowAddWidget(),
                                      child: InkWell(
                                        child: CommonUtils.getSimpleText('添加', 16, Colours.base_primary),
                                        onTap: () {
                                          ///查看详情，去添加加班记录
                                          BoostNavigator.instance.push('WorkOvertimeAddPage', arguments: {
                                            'user_uuid': '${_controller.myAttendanceOne?.userUuid}',
                                            'user_name': '${_controller.myAttendanceOne?.userName}',
                                            'user_avatar': '${_controller.myAttendanceOne?.avatar}',
                                            'current_date': _controller.currentDateDay.value,
                                            'user_project_name': '${_controller.myAttendanceOne?.projectName}',
                                            'project_uuid': '${_controller.myAttendanceOne?.projectUuid}',
                                            'project_name': '${_controller.myAttendanceOne?.projectName}',
                                          }).then((value) {
                                            _presenter?.delayRefreshAttendanceOne();
                                          });
                                        },
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(left: 16, right: 16, bottom: 10),
                                padding: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.overtimeList == null || _controller.myAttendanceDay!.overtimeList!.isEmpty) ? const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10) : const EdgeInsets.only(top: 0, bottom: 0, left: 0, right: 0),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.overtimeList == null || _controller.myAttendanceDay!.overtimeList!.isEmpty) ? Colours.base_primary_bg_page : Colours.transparent,
                                  borderRadius: BorderRadius.circular((_controller.myAttendanceDay == null || _controller.myAttendanceDay?.overtimeList == null || _controller.myAttendanceDay!.overtimeList!.isEmpty) ? 0 : 4.0),
                                ),
                                child: Column(
                                  children: [
                                    Visibility(
                                      visible: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.overtimeList == null || _controller.myAttendanceDay!.overtimeList!.isEmpty),
                                      child: CommonUtils.getSimpleText('暂无加班记录', 15, Colours.base_primary_text_title, textAlign: TextAlign.center),
                                    ),
                                    Visibility(
                                      visible: (_controller.myAttendanceDay != null && _controller.myAttendanceDay?.overtimeList != null && _controller.myAttendanceDay!.overtimeList!.isNotEmpty),
                                      child: ListView.builder(
                                          physics: const NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount: _controller.myAttendanceDay?.overtimeList?.length,
                                          itemBuilder: (_, index) {
                                            return AttendanceWorkOvertimeRecordListItem(
                                              data: _controller.myAttendanceDay!.overtimeList![index],
                                              onClick: () {
                                                BoostNavigator.instance.push('WorkOvertimeOnePage', arguments: {
                                                  'uuid': '${_controller.myAttendanceDay!.overtimeList![index].uuid}',
                                                }).then((value) {
                                                  requestOne();
                                                });
                                              },
                                            );
                                          }),
                                    ),
                                  ],
                                ),
                              ),

                              ///请假记录
                              Padding(
                                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Expanded(child: CommonUtils.getSimpleText('请假记录', 18, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                                    Visibility(
                                      visible: _shouldShowAddWidget(),
                                      child: InkWell(
                                        child: CommonUtils.getSimpleText('添加', 16, Colours.base_primary),
                                        onTap: () {
                                          ///查看详情，去添加请假记录
                                          BoostNavigator.instance.push('HolidayAddPage', arguments: {
                                            'user_uuid': '${_controller.myAttendanceOne?.userUuid}',
                                            'user_name': '${_controller.myAttendanceOne?.userName}',
                                            'user_avatar': '${_controller.myAttendanceOne?.avatar}',
                                            'current_date': _controller.currentDateDay.value,
                                            'user_project_name': '${_controller.myAttendanceOne?.projectName}',
                                            'project_uuid': '${_controller.myAttendanceOne?.projectUuid}',
                                            'project_name': '${_controller.myAttendanceOne?.projectName}',
                                          }).then((value) {
                                            _presenter?.delayRefreshAttendanceOne();
                                          });
                                        },
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(left: 16, right: 16, bottom: 10),
                                padding: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.holidayList == null || _controller.myAttendanceDay!.holidayList!.isEmpty) ? const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10) : const EdgeInsets.only(top: 0, bottom: 0, left: 0, right: 0),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.holidayList == null || _controller.myAttendanceDay!.holidayList!.isEmpty) ? Colours.base_primary_bg_page : Colours.transparent,
                                  borderRadius: BorderRadius.circular((_controller.myAttendanceDay == null || _controller.myAttendanceDay?.holidayList == null || _controller.myAttendanceDay!.holidayList!.isEmpty) ? 0 : 4.0),
                                ),
                                child: Column(
                                  children: [
                                    Visibility(
                                      visible: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.holidayList == null || _controller.myAttendanceDay!.holidayList!.isEmpty),
                                      child: CommonUtils.getSimpleText('暂无请假记录', 15, Colours.base_primary_text_title, textAlign: TextAlign.center),
                                    ),
                                    Visibility(
                                      visible: (_controller.myAttendanceDay != null && _controller.myAttendanceDay?.holidayList != null && _controller.myAttendanceDay!.holidayList!.isNotEmpty),
                                      child: ListView.builder(
                                          physics: const NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount: _controller.myAttendanceDay?.holidayList?.length,
                                          itemBuilder: (_, index) {
                                            return AttendanceHolidayRecordListItem(
                                              data: _controller.myAttendanceDay!.holidayList![index],
                                              onClick: () {
                                                BoostNavigator.instance.push('HolidayOnePage', arguments: {
                                                  'uuid': '${_controller.myAttendanceDay!.holidayList![index].uuid}',
                                                }).then((value) {
                                                  requestOne();
                                                });
                                              },
                                            );
                                          }),
                                    ),
                                  ],
                                ),
                              ),

                              ///修改记录
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                                    child: CommonUtils.getSimpleText('修改记录', 18, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                              Container(
                                margin: const EdgeInsets.only(left: 16, right: 16, bottom: 50),
                                padding: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.operateLogList == null || _controller.myAttendanceDay!.operateLogList!.isEmpty) ? const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10) : const EdgeInsets.only(top: 0, bottom: 0, left: 0, right: 0),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.operateLogList == null || _controller.myAttendanceDay!.operateLogList!.isEmpty) ? Colours.base_primary_bg_page : Colours.transparent,
                                  borderRadius: BorderRadius.circular((_controller.myAttendanceDay == null || _controller.myAttendanceDay?.operateLogList == null || _controller.myAttendanceDay!.operateLogList!.isEmpty) ? 0 : 4.0),
                                ),
                                child: Column(
                                  children: [
                                    Visibility(
                                      visible: (_controller.myAttendanceDay == null || _controller.myAttendanceDay?.operateLogList == null || _controller.myAttendanceDay!.operateLogList!.isEmpty),
                                      child: CommonUtils.getSimpleText('暂无操作记录', 15, Colours.base_primary_text_title, textAlign: TextAlign.center),
                                    ),
                                    Visibility(
                                      visible: (_controller.myAttendanceDay != null && _controller.myAttendanceDay?.operateLogList != null && _controller.myAttendanceDay!.operateLogList!.isNotEmpty),
                                      child: ListView.builder(
                                          physics: const NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount: _controller.myAttendanceDay?.operateLogList?.length,
                                          itemBuilder: (_, index) {
                                            return AttendanceOperateLogRecordListItem(
                                              data: _controller.myAttendanceDay!.operateLogList![index],
                                              showDel: _shouldShowAddWidget(),
                                              onClick: () {
                                                ///删除该修改记录
                                                DialogManager.showConfirmDialog(
                                                  context: context,
                                                  title: '提示',
                                                  cancel: '取消',
                                                  confirm: '确定',
                                                  message: '是否删除该条修改记录？',
                                                  onConfirm: () {
                                                    _presenter?.deleteOperateLog(_controller.myAttendanceDay!.operateLogList![index].uuid ?? '');
                                                  },
                                                  onCancel: () {},
                                                );
                                              },
                                            );
                                          }),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ))
                    ],
                  ),
                )),
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AttendanceOnePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void getMyAttendanceOne(MyAttendanceOneEntity data) {
    ///拿到详情
    _controller.getAttendanceStat(data);

    ///日历数据的绘制
    data.list?.forEach((element) {
      _controller.dateToUuidMap[element.kqDate ?? ''] = element.userUuid ?? ''; // 将日期和 uuid 映射
    });

    ///增加之前先清空
    _controller.tags.clear();

    data.list?.forEach((element) {
      DateTime date = DateTime.parse(element.kqDate ?? '');
      List<CalendarTag> tagList = [];

      int schemeCount = 0; // 计数器，限制最大标签数为 4

      // 检查每个 "is" 字段并添加相应的标签
      if (element.isCq == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFF09BE89), description: "出勤"));
        schemeCount++;
      }
      if (element.isXx == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFF8D8E99), description: "休息"));
        schemeCount++;
      }
      if (element.isXj == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFF800080), description: "请假"));
        schemeCount++;
      }
      if (element.isKg == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFFFF4040), description: "旷工"));
        schemeCount++;
      }
      if (element.isQk == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFFFF4040), description: "缺卡"));
        schemeCount++;
      }
      if (element.isJb == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFF1890FF), description: "加班"));
        schemeCount++;
      }
      if (element.isCd == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFFFF4040), description: "迟到"));
        schemeCount++;
      }
      if (element.isZt == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFFFF4040), description: "早退"));
        schemeCount++;
      }
      if (element.isEntry == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFF1890FF), description: "入职"));
        schemeCount++;
      }
      if (element.isLeave == "1" && schemeCount < 4) {
        tagList.add(CalendarTag(uuid: element.uuid, color: Color(0xFF1890FF), description: "离职"));
      }
      // 更新最大 schemeCount
      if (schemeCount > _controller.maxSchemeCount.value) {
        _controller.maxSchemeCount.value = schemeCount;
      }
      // 将标签添加到日期中
      if (tagList.isNotEmpty) {
        _controller.tags[date] = tagList; // 更新标签
      }
    });

    print('map ---> ${_controller.dateToUuidMap}');
    print('map ---> ${_controller.currentDateDay.value}');
    print('大小---${_controller.maxSchemeCount.value}');

    ///再获取当天的考勤数据
    _presenter?.getMyAttendanceDayOne();
  }

  @override
  void getMyAttendanceDayOne(MyAttendanceDayEntity data) {
    ///获取到了当天的考勤详情 进行处理
    setState(() {
      _controller.myAttendanceDay = data;
      print('我是list 大小 ${data.list?.length}');
    });
  }

  @override
  void getClasses(ProjectClassesDataEntity dataEntity, String? userName, String? attendanceDate) {
    DialogManager.setDialogShowing(true);

    ///拿到当前人的班次列表
    //往数据中增加一个休息的班次
    ProjectClassesDataList data = ProjectClassesDataList();
    data.uuid = "0";
    data.className = "休息日";
    data.classTime = "该日无需上班";
    dataEntity.list?.insert(0, data);
    //展示log
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext dialogContext) {
        return CustomAttendanceClassesDialog(
          dataEntity: dataEntity,
          classUuid: _controller.myAttendanceDay?.classUuid ?? '',
          userName: userName,
          attendanceDate: attendanceDate,
          onClick: (uuid) {
            ///如果不等于空，那么就请求
            if (!TextUtil.isEmpty(uuid)) {
              _presenter?.updateClassType(_controller.myAttendanceDay?.scheduleUuid ?? '', uuid, (uuid == '0') ? '1' : '2');
            }
            DialogManager.setDialogShowing(false);
            Navigator.of(dialogContext).pop(); // 使用 dialogContext 关闭对话框
          },
        );
      },
    );
  }

  void showLookAttendanceDialog(bool isInWork, MyAttendanceDayList data) {
    DialogManager.setDialogShowing(true);
    BrnBottomPicker.show(context,
        showTitle: false,
        contentWidget: SizedBox(
          height: 400,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Gaps.vGap20,
              Row(
                children: [
                  Gaps.hGap32,
                  Expanded(
                    child: Center(
                      child: CommonUtils.getSimpleText(
                        '查看打卡记录',
                        20,
                        Colours.base_primary_text_title,
                      ),
                    ),
                  ),
                  InkWell(
                    child: LoadImage(
                      'base/icon_base_close',
                      width: 24,
                      height: 24,
                    ),
                    onTap: () {
                      DialogManager.setDialogShowing(false);
                      Navigator.of(context, rootNavigator: true).pop();
                    },
                  ),
                  Gaps.hGap20,
                ],
              ),
              Gaps.vGap20,
              Gaps.line,
              Gaps.vGap20,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    CommonUtils.getSimpleText('应打卡时间：', 16, Colours.base_primary_text_title),
                    Expanded(child: CommonUtils.getSimpleText(isInWork ? data.inShouldClockTime ?? '-' : data.outShouldClockTime ?? '-', 16, Colours.base_primary_text_title, textAlign: TextAlign.right)),
                  ],
                ),
              ),
              Gaps.vGap10,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    CommonUtils.getSimpleText('实打卡时间：', 16, Colours.base_primary_text_title),
                    Expanded(child: CommonUtils.getSimpleText(isInWork ? data.inActualClockTime ?? '-' : data.outActualClockTime ?? '-', 16, Colours.base_primary_text_title, textAlign: TextAlign.right)),
                  ],
                ),
              ),
              Gaps.vGap10,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    CommonUtils.getSimpleText('打卡类型：', 16, Colours.base_primary_text_title),
                    Expanded(child: CommonUtils.getSimpleText(isInWork ? data.inClockTypeName ?? '-' : data.outClockTypeName ?? '-', 16, Colours.base_primary_text_title, textAlign: TextAlign.right)),
                  ],
                ),
              ),
              Gaps.vGap10,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    CommonUtils.getSimpleText('操作人：', 16, Colours.base_primary_text_title),
                    Expanded(child: CommonUtils.getSimpleText(isInWork ? data.inOperateName ?? '-' : data.outOperateName ?? '-', 16, Colours.base_primary_text_title, textAlign: TextAlign.right)),
                  ],
                ),
              ),
              Visibility(
                  visible: (!TextUtil.isEmpty(data.inMediaUrl)) && isInWork,
                  child: InkWell(
                    child: Column(
                      children: [
                        Gaps.vGap10,
                        LoadImage(
                          data.inMediaUrl ?? '',
                          width: 100,
                          height: 100,
                        ),
                      ],
                    ),
                    onTap: () {
                      ImagePickers.previewImage(data.inMediaUrl ?? '');
                    },
                  )),
              Visibility(
                  visible: (!TextUtil.isEmpty(data.outMediaUrl)) && !isInWork,
                  child: InkWell(
                    child: Column(
                      children: [
                        Gaps.vGap10,
                        LoadImage(
                          data.outMediaUrl ?? '',
                          width: 100,
                          height: 100,
                        ),
                      ],
                    ),
                    onTap: () {
                      ImagePickers.previewImage(data.outMediaUrl ?? '');
                    },
                  ))
            ],
          ),
        ));
  }

  ///休息日的弹窗，不用处理，单纯的展示
  void showHolidayDialog() {
    DialogManager.setDialogShowing(true);
    BrnBottomPicker.show(context,
        showTitle: false,
        contentWidget: SizedBox(
          height: 400,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Gaps.vGap20,
              Row(
                children: [
                  Gaps.hGap10,
                  Expanded(child: CommonUtils.getSimpleText('修改考勤结果', 20, Colours.base_primary_text_title)),
                  InkWell(
                    child: LoadImage(
                      'base/icon_base_close',
                      width: 24,
                      height: 24,
                    ),
                    onTap: () {
                      DialogManager.setDialogShowing(false);
                      Navigator.of(context, rootNavigator: true).pop();
                    },
                  ),
                  Gaps.hGap10,
                ],
              ),
              Gaps.vGap20,
              Gaps.line,
              Gaps.vGap50,
              LoadImage(
                'no_data',
                width: 80,
                height: 80,
              ),
              Gaps.vGap10,
              CommonUtils.getSimpleText('休息日不需要打卡，不会有异常，无需修改', 15, Colours.base_primary_text_title),
            ],
          ),
        ));
  }

  ///修改考勤结果
  void showAttendanceDialog(String stat_uuid) {
    BoostNavigator.instance.push('AttendanceResultRulesPage', arguments: {
      'user_name': widget.userName,
      'uuid': stat_uuid,
      'hideMinutes': ('2' != _controller.myAttendanceDay?.classType),
    }).then((value) {
      if (value is String) {
        ///取消修改，按照考勤为准
        _presenter?.requestResetAttendance(value);
      } else if (value is AttendanceUpdate) {
        ///修改考勤结果
        HashMap<String, String> params = HashMap<String, String>();
        params['stat_uuid'] = '$stat_uuid';
        if (value.cdStatus == -1 || value.cdStatus == 2) {
          params['sb_status'] = '3';
        } else {
          params['sb_status'] = '${value.cdStatus + 1}';
        }
        params['cd_time_long'] = value.cdLong;

        if (value.ztStatus == -1 || value.ztStatus == 2) {
          params['xb_status'] = '3';
        } else {
          params['xb_status'] = '${value.ztStatus + 1}';
        }
        params['zt_time_long'] = value.cdLong;
        _presenter?.requestUpdateAttendance(params);
      }
    });
  }

  ///回到终点
  void autoToZero() {
    _scrollController.animateTo(
      0.0, // 滚动到顶部位置
      duration: Duration(milliseconds: 300), // 动画持续时间
      curve: Curves.easeOut, // 动画曲线
    );
  }

  @override
  void startGetMyAttendanceOne() {
    autoToZero();
  }
}
