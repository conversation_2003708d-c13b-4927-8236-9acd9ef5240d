import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/role_custom_data.dart';

import '../../../../generated/role_detail_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_app_bar.dart';
import '../../company/bean/department_company_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/rules_special_data_entity.dart';
import '../controller/work_rules_controller.dart';
import '../controller/work_rules_role_permission_controller.dart';
import '../controller/work_rules_special_page_controller.dart';
import '../iview/work_post_save_iview.dart';
import '../iview/work_rules_role_permission_save_iview.dart';
import '../iview/work_rules_save_iview.dart';
import '../iview/work_rules_special_permission_save_iview.dart';
import '../presenter/work_rules_persenter.dart';
import '../presenter/work_rules_role_permission_persenter.dart';
import '../presenter/work_rules_special_permission_persenter.dart';

/// 特殊权限
class AddSpecialPermissionPage extends StatefulWidget {
  RulesSpecialDataList? data;

  AddSpecialPermissionPage({Key? key, this.data}) : super(key: key);

  @override
  _AddSpecialPermissionPageState createState() => _AddSpecialPermissionPageState();
}

class _AddSpecialPermissionPageState extends State<AddSpecialPermissionPage> with BasePageMixin<AddSpecialPermissionPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<AddSpecialPermissionPage> implements WorkRulesSpecialPermissionSaveIView {
  WorkRulesSpecialPermissionPagePresenter? _presenter;

  final WorkRulesSpecialManagerPageController _controller = WorkRulesSpecialManagerPageController();

  String title = '添加特殊数据权限';

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.data?.uuid)) {
      title = '编辑特殊数据权限';
      _controller.staffName.value = widget.data?.userName ?? '';
      _controller.staffUuid.value = widget.data?.uuid ?? '';
      _controller.companyName.value = getBuildName(widget.data!);
      _controller.companyUuids.value = getBuildCompanyUuids(widget.data!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: title,
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Column(
        children: [
          Obx(() => BrnTextSelectFormItem(
                title: "员工",
                value: _controller.staffName.value,
                isRequire: true,
                onTap: () {
                  List<String> userJson = [];
                  userJson.add(_controller.staffUuid.value);
                  BoostNavigator.instance.push('selectPersonnelPage', arguments: {
                    'title': '添加特殊数据权限',
                    'is_head_office': '1',
                    'status': '1',
                    'uuids': json.encode(userJson),
                  }).then((value) {
                    if (value != null) {
                      List<ProjectArchivesList> data = value as List<ProjectArchivesList>;
                      if (data.isNotEmpty) {
                        _controller.staffName.value = data[0].userName ?? "";
                        _controller.staffUuid.value = data[0].uuid ?? "";
                      }
                    }
                  });
                },
              )),
          Gaps.line,
          Obx(() => BrnTextSelectFormItem(
                title: "数据权限范围",
                value: _controller.companyName.value,
                isRequire: true,
                onTap: () {
                  ///跳转到其他界面选择上级部门
                  BoostNavigator.instance.push('companyChoiceAdministrationPage', arguments: {'multiple': true, "choice_uuids": _controller.companyUuids}).then((value) {
                    if (value != null) {
                      List<DepartmentCompanyList> departmentCompanyList = value as List<DepartmentCompanyList>;
                      _controller.companyUuids.value = departmentCompanyList.map((e) => e.uuid ?? '').toList();
                      _controller.companyName.value = departmentCompanyList.map((e) => e.departmentName ?? '').join(',');
                    }
                  });
                },
              )),
        ],
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        height: 54,
        child: Column(
          children: [
            Gaps.line,
            // 按钮
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, top: 7, bottom: 6),
              child: BrnBigMainButton(
                themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
                title: '确定',
                onTap: () {
                  if (TextUtil.isEmpty(_controller.staffName.value)) {
                    BrnToast.show('请选择员工', context);
                    return;
                  }
                  if (TextUtil.isEmpty(_controller.companyName.value)) {
                    BrnToast.show('请选择数据权限范围', context);
                    return;
                  }
                  _presenter?.saveWorkSpecial();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkRulesSpecialPermissionPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  viewRefresh() {
    BrnToast.show('操作成功', context);
    BoostNavigator.instance.pop();
  }

  String getBuildName(RulesSpecialDataList data) {
    StringBuffer stringBuffer = StringBuffer();
    _controller.companyUuids.clear();

    if (data.departmentList != null && data.departmentList!.isNotEmpty) {
      for (int i = 0; i < data.departmentList!.length; i++) {
        stringBuffer.write(data.departmentList![i].departmentName);
        if (!TextUtil.isEmpty(data.departmentList![i].uuid)) {
          _controller.companyUuids.add(data.departmentList![i].uuid!);
        }
        if (i < data.departmentList!.length - 1) {
          stringBuffer.write(","); // 在名称后添加逗号，除了最后一个
        }
      }
    }

    return stringBuffer.toString();
  }

  List<String> getBuildCompanyUuids(RulesSpecialDataList data) {
    List<String> stringBuffer = [];

    if (data.departmentList != null && data.departmentList!.isNotEmpty) {
      for (int i = 0; i < data.departmentList!.length; i++) {
        stringBuffer.add(data.departmentList![i].uuid ?? '');
      }
    }
    return stringBuffer;
  }
}
