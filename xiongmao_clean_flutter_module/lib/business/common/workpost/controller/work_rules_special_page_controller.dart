import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../bean/rules_special_data_entity.dart';
import '../bean/work_post_manager_entity.dart';

class WorkRulesSpecialManagerPageController extends GetxController {


  var list = <RulesSpecialDataList>[].obs;

  var totalNumber = "0".obs;

  void initMyList(int total, List<RulesSpecialDataList> value) {
    totalNumber.value = total.toString();
    list.value = value.toList();
  }

  void updateMyList(List<RulesSpecialDataList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///员工
  var staffName = "".obs;

  ///员工uuid
  var staffUuid = "".obs;

  ///数据权限范围
  var companyName = "".obs;

  ///用来记录多选的uuids
  var companyUuids = <String>[].obs;
}
