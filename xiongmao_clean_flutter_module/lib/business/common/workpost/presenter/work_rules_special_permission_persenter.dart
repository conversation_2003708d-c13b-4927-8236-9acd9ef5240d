import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_rules_config_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../bean/rules_special_data_entity.dart';
import '../controller/work_rules_role_permission_controller.dart';
import '../controller/work_rules_special_page_controller.dart';
import '../iview/work_rules_role_permission_save_iview.dart';
import '../iview/work_rules_special_permission_save_iview.dart';

class WorkRulesSpecialPermissionPagePresenter extends BasePagePresenter<WorkRulesSpecialPermissionSaveIView> with WidgetsBindingObserver {
  WorkRulesSpecialManagerPageController controller;

  WorkRulesSpecialPermissionPagePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getSpecialListManager();
  }

  void loadMore() {
    _page++;
    getSpecialListManager();
  }

  ///获取特殊权限
  Future<dynamic> getSpecialListManager() {
    var params = <String, String>{};
    params["page"] = "$_page";
    return requestNetwork<RulesSpecialDataEntity>(Method.get, url: HttpApi.RULES_SPECIAL_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  ///删除
  Future<dynamic> delSpecial(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<Object>(Method.post, url: HttpApi.RULES_SPECIAL_DEL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }

  ///保存
  Future<dynamic> saveWorkSpecial() {
    var params = <String, String>{};
    params["uuid"] = controller.staffUuid.value;
    params["department_uuid_list"] = controller.companyUuids.value.join(',');
    return requestNetwork<Object>(Method.post, url: HttpApi.RULES_SPECIAL_SAVE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }
}
