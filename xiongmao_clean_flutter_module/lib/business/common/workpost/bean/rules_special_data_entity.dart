import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/rules_special_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/rules_special_data_entity.g.dart';

@JsonSerializable()
class RulesSpecialDataEntity {
	int? page;
	int? size;
	int? total;
	List<RulesSpecialDataList>? list;

	RulesSpecialDataEntity();

	factory RulesSpecialDataEntity.fromJson(Map<String, dynamic> json) => $RulesSpecialDataEntityFromJson(json);

	Map<String, dynamic> toJson() => $RulesSpecialDataEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class RulesSpecialDataList {
	String? uuid;
	@JSONField(name: "user_name")
	String? userName;
	@JSONField(name: "department_list")
	List<RulesSpecialDataListDepartmentList>? departmentList;

	RulesSpecialDataList();

	factory RulesSpecialDataList.fromJson(Map<String, dynamic> json) => $RulesSpecialDataListFromJson(json);

	Map<String, dynamic> toJson() => $RulesSpecialDataListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class RulesSpecialDataListDepartmentList {
	String? uuid;
	@JSONField(name: "department_name")
	String? departmentName;

	RulesSpecialDataListDepartmentList();

	factory RulesSpecialDataListDepartmentList.fromJson(Map<String, dynamic> json) => $RulesSpecialDataListDepartmentListFromJson(json);

	Map<String, dynamic> toJson() => $RulesSpecialDataListDepartmentListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}