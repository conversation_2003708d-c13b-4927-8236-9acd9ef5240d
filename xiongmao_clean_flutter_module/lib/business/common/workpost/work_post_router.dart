import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/todo/page/todo_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/add_special_permission_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/role_permission_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/special_permission_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_post_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_rules_page.dart';

import '../../../net/http_config.dart';
import 'bean/rules_special_data_entity.dart';

/// 定义页面名称
const workPostPage = "workPostPage";

const addWorkPost = "addWorkPost";

///用工规则
const workRulesPage = "workRulesPage";

///各角色权限
const rolePermissionPage = "rolePermissionPage";

///特殊权限
const specialPermissionPage = "specialPermissionPage";

///添加特殊权限
const addSpecialPermissionPage = "addSpecialPermissionPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> workPostRouterMap = {
  workPostPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return WorkPostPage();
        });
  },
  addWorkPost: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map["uuid"];
          String? work_name = map["work_name"];
          String? work_salary = map["work_salary"];
          return WorkAddPostPage(
            uuid: uuid,
            work_name: work_name,
            work_salary: work_salary,
          );
        });
  },

  ///用工规则
  workRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return WorkRulesPage();
        });
  },

  ///各角色权限
  rolePermissionPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return RolePermissionPage();
        });
  },

  ///特殊权限
  specialPermissionPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return SpecialPermissionPage();
        });
  },

  ///添加特殊权限
  addSpecialPermissionPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          RulesSpecialDataList? data = map["data"];
          return AddSpecialPermissionPage(
            data: data,
          );
        });
  },
};
