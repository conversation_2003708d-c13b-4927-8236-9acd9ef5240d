import 'package:get/get.dart';
import '../bean/department_company_entity.dart';

class CompanyChoiceAdministrationController extends GetxController {
  /// 用来记录反选的 UUIDs
  var invertUuids = <String>[].obs;

  /// 父部门 UUID
  var parentUuid = ''.obs;

  /// 根部门数据
  var data = DepartmentCompanyEntity().obs;

  /// 子部门列表
  var list = <DepartmentCompanyList>[].obs;

  /// 已选择的部门列表
  var choiceDepartment = <DepartmentCompanyList>[].obs;

  void initMyList(List<DepartmentCompanyList> value) {
    // 初始化根部门的选中状态
    if (invertUuids.contains(data.value.uuid)) {
      data.value.isSelected = true;
      DepartmentCompanyList dataNew = DepartmentCompanyList();
      dataNew.departmentName = data.value.departmentName;
      dataNew.departmentTypeName = data.value.departmentTypeName;
      dataNew.isSelected = data.value.isSelected;
      dataNew.responseUserList = data.value.responseUserList;
      dataNew.uuid = data.value.uuid;
      dataNew.isHasChild = data.value.isHasChild;
      dataNew.responseUserList = [];
      setChoiceDepartment(dataNew);
    }

    // 初始化子部门的选中状态
    for (var element in value) {
      element.isSelected = invertUuids.contains(element.uuid);
      if (element.isSelected) {
        setChoiceDepartment(element);
      }
    }

    // 对比 value 中跟 choiceDepartment 里面对应的吗，改变选中状态
    for (var element in value) {
      if (choiceDepartment.any((element2) => element2.uuid == element.uuid)) {
        element.isSelected = true;
      }
    }

    // 同时也需要循环一下 choiceDepartment 看有没有跟 data.uuid 对比一样的
    for (var element in choiceDepartment) {
      if (element.uuid == data.value.uuid) {
        data.value.isSelected = true;
      }
    }

    // 更新列表
    list.assignAll(value);
  }

  /// 设置选中的部门
  void setChoiceDepartment(DepartmentCompanyList value) {
    if (value.isSelected == true) {
      // 确保不重复添加
      if (!choiceDepartment.any((element) => element.uuid == value.uuid)) {
        choiceDepartment.add(value);
      }
    } else {
      // 移除已选中的部门
      choiceDepartment.removeWhere((element) => element.uuid == value.uuid);

      // 对比根目录进行状态改变
      if (value.uuid == data.value.uuid) {
        data.value.isSelected = false;
      }
    }
    choiceDepartment.refresh();
  }

  /// 删除已选择的部门
  void choiceDelete(DepartmentCompanyList value) {
    // 更新列表中的选中状态
    for (var element in list) {
      if (element.uuid == value.uuid) {
        element.isSelected = false;
      }
    }

    // 从已选择的部门列表中移除
    choiceDepartment.removeWhere((element) => element.uuid == value.uuid);

    // 如果删除的是根目录，改变根目录的选中状态
    if (value.uuid == data.value.uuid) {
      data.value.isSelected = false;
    }

    // 从反选 UUIDs 中移除
    invertUuids.remove(value.uuid);

    // 刷新列表和已选择的部门列表
    list.refresh();
    choiceDepartment.refresh();
  }

  /// 构建负责人名称字符串
  String getBuildName(List<DepartmentCompanyListResponseUserList>? data) {
    if (data == null || data.isEmpty) {
      return '无';
    }
    return data.map((user) => user.userName).join(',');
  }
}
