import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../bean/department_company_entity.dart';
import '../controller/company_choice_administration_controller.dart';
import '../item/administration_choice_list_Item.dart';
import '../item/administration_list_Item.dart';
import '../iview/administration_iview.dart';
import '../persenter/company_choice_administration_persenter.dart';

/// 进行选择的列表
class CompanyChoiceAdministrationPage extends StatefulWidget {
  ///判断是多选 还是 单选
  bool multiple;

  ///拿到id
  String? uuid;

  ///selfuuid
  String? selfUuid;

  ///别的洁面拿到的数据来做反选
  List<String>? choice_uuids;

  CompanyChoiceAdministrationPage({super.key, required this.multiple, this.uuid, this.selfUuid, this.choice_uuids});

  @override
  _CompanyChoiceAdministrationPage createState() => _CompanyChoiceAdministrationPage();
}

class _CompanyChoiceAdministrationPage extends State<CompanyChoiceAdministrationPage> with BasePageMixin<CompanyChoiceAdministrationPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CompanyChoiceAdministrationPage> implements AdministrationIView {
  CompanyChoiceAdministrationPresenter? _presenter;

  final CompanyChoiceAdministrationController _controller = CompanyChoiceAdministrationController();

  ///底部导航的滑动
  ScrollController _scrollController = ScrollController();

  /// 导航路径
  final RxList<DepartmentCompanyEntity> _navigationPath = <DepartmentCompanyEntity>[].obs;

  @override
  void initState() {
    super.initState();

    ///反选的uuid
    _controller.invertUuids.value = widget.choice_uuids ?? [];
    _controller.parentUuid.value = widget.uuid ?? '';
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.getDepartmentListManager();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.getDepartmentListManager();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (_navigationPath.isNotEmpty) {
          // 返回上一级
          var previousPath = _navigationPath.removeLast();
          _controller.parentUuid.value = previousPath.uuid!;
          _presenter?.getDepartmentListManager();
          return false; // 不执行默认的 pop 操作
        } else {
          return true; // 执行默认的 pop 操作
        }
      },
      child: Obx(() => Scaffold(
            backgroundColor: Colours.white,
            appBar: MyAppBar(
                centerTitle: widget.multiple ? '多选部门' : '单选部门',
                actionName: widget.multiple ? '不限制' : '',
                onPressed: () {
                  BoostNavigator.instance.pop();
                },
                onBack: () {
                  if (_navigationPath.isNotEmpty) {
                    // 返回上一级
                    var previousPath = _navigationPath.removeLast();
                    _controller.parentUuid.value = previousPath.uuid!;
                    _presenter?.getDepartmentListManager();
                  } else {
                    BoostNavigator.instance.pop();
                  }
                }),
            body: Column(
              children: [
                InkWell(
                  child: Container(
                    padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
                    child: Row(
                      children: [
                        InkWell(
                          child: Padding(
                            padding: const EdgeInsets.all(10),
                            child: LoadImage(
                              (_controller.data.value.isSelected == true) ? 'icon_check' : 'icon_uncheck',
                              width: 20,
                              height: 20,
                            ),
                          ),
                          onTap: () {
                            rootClick();
                          },
                        ),
                        Gaps.hGap10,
                        LoadImage(
                          'common/icon_common_company',
                          width: 40,
                          height: 40,
                        ),
                        Gaps.hGap10,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CommonUtils.getSimpleText(_controller.data.value.departmentName, 14, Colours.base_primary_text_title),
                                Gaps.hGap10,
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colours.base_primary_select,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: CommonUtils.getSimpleText(_controller.data.value.departmentTypeName, 12, Colours.base_primary),
                                ),
                              ],
                            ),
                            Visibility(
                              child: CommonUtils.getSimpleText('合同公司：${_controller.data.value.contractCompanyName}', 12, Colours.base_primary_text_caption),
                              visible: (!TextUtil.isEmpty(_controller.data.value.contractCompanyName)),
                            ),
                            CommonUtils.getSimpleText('负责人：${_controller.getBuildName(_controller.data.value.responseUserList)}', 12, Colours.base_primary_text_caption),
                          ],
                        )
                      ],
                    ),
                  ),
                  onTap: () {
                    rootClick();
                  },
                ),
                Gaps.lineLeftMargin,
                Expanded(
                  child: MyRefreshListView(
                    itemCount: _controller.list.length,
                    onRefresh: _onRefresh,
                    loadMore: _loadMore,
                    hasMore: false,
                    itemBuilder: (_, index) {
                      return AdministrationChoiceListItem(
                        data: _controller.list[index],
                        gotoLevel: () {
                          if (widget.selfUuid == _controller.data.value.uuid) {
                            //说明是同级，不让继续了
                            BrnToast.show('不可以选择自己的下级', context);
                            return;
                          } else if (widget.selfUuid == _controller.list[index].uuid) {
                            //说明是同级，不让继续了
                            BrnToast.show('不可以选择自己的下级', context);
                            return;
                          }

                          ///isHasChild 如果是1，说明有下级，可以继续
                          if (_controller.list[index].isHasChild == '1') {
                            // 更新导航路径
                            _navigationPath.add(_controller.data.value);
                            // 更新父UUID
                            _controller.parentUuid.value = _controller.list[index].uuid!;
                            // 请求子部门数据
                            _presenter?.getDepartmentListManager();
                          } else {
                            departmentClick(index);
                          }
                        },
                        check: () {
                          departmentClick(index);
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
            bottomNavigationBar: Visibility(
              visible: widget.multiple,
              child: Container(
                color: Colors.white,
                height: 58,
                child: Column(
                  children: [
                    Gaps.line,
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            controller: _scrollController,
                            child: Row(
                              children: _controller.choiceDepartment.map((departmentName) {
                                int index = _controller.choiceDepartment.indexOf(departmentName);
                                return Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                                  child: InkWell(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colours.base_primary_bg_page,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          CommonUtils.getSimpleText(_controller.choiceDepartment[index].departmentName ?? '', 14, Colours.base_primary_text_title),
                                          const SizedBox(width: 4),
                                          const Icon(Icons.close, size: 16),
                                        ],
                                      ),
                                    ),
                                    onTap: () {
                                      // 移除当前路径及其之后的所有路径
                                      _controller.choiceDelete(_controller.choiceDepartment[index]);
                                    },
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                        Gaps.hGap10,
                        Expanded(
                          child: InkWell(
                            child: Container(
                              height: 36,
                              margin: const EdgeInsets.only(top: 10),
                              decoration: BoxDecoration(
                                color: Colours.base_primary,
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                              alignment: Alignment.center,
                              child: CommonUtils.getSimpleText('确定(${_controller.choiceDepartment.length})', 16, Colours.white, textAlign: TextAlign.center),
                            ),
                            onTap: () {
                              BoostNavigator.instance.pop(_controller.choiceDepartment);
                            },
                          ),
                        ),
                        Gaps.hGap10,
                      ],
                    ),
                  ],
                ),
              ),
            ),
          )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CompanyChoiceAdministrationPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  void departmentClick(int index) {
    if (!widget.multiple) {
      if (widget.selfUuid == _controller.data.value.uuid) {
        //说明是同级，不让继续了
        BrnToast.show('不可以选择自己的下级', context);
        return;
      }
      BoostNavigator.instance.pop(_controller.list[index]); // 直接关闭界面并返回UUID
    } else {
      ///如果没下级，那么就改变状态 并且把当前对象添加到选中的列表中
      _controller.list[index].isSelected = !_controller.list[index].isSelected;
      _controller.list.refresh();
      _controller.setChoiceDepartment(_controller.list[index]);
      autoScroll();
    }
    setState(() {});
  }

  void rootClick() {
    ///如果是单选，直接关闭界面并返回UUID
    _controller.data.value.isSelected = !_controller.data.value.isSelected;

    ///创建一个新的对象，来存放当前是否已经选中
    DepartmentCompanyList data = DepartmentCompanyList();
    data.departmentName = _controller.data.value.departmentName;
    data.departmentTypeName = _controller.data.value.departmentTypeName;
    data.isSelected = _controller.data.value.isSelected;
    data.responseUserList = _controller.data.value.responseUserList;
    data.uuid = _controller.data.value.uuid;
    data.isHasChild = _controller.data.value.isHasChild;
    data.responseUserList = [];

    ///单选
    if (!widget.multiple) {
      if (widget.selfUuid == _controller.data.value.uuid) {
        //说明是同级，不让继续了
        BrnToast.show('不能选择自己当成上级', context);
        return;
      }
      BoostNavigator.instance.pop(data); // 直接关闭界面并返回UUID
    } else {
      _controller.setChoiceDepartment(data);
      autoScroll();
    }
    setState(() {});
  }

  @override
  bool get wantKeepAlive => true;

  @override
  viewRefresh() {}

  autoScroll() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  editOne(DepartmentCompanyEntity data) {}
}
