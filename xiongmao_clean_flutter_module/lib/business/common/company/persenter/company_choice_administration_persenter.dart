import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../bean/department_company_entity.dart';
import '../controller/company_administration_controller.dart';
import '../controller/company_choice_administration_controller.dart';
import '../iview/administration_iview.dart';

class CompanyChoiceAdministrationPresenter extends BasePagePresenter<AdministrationIView> with WidgetsBindingObserver {
  CompanyChoiceAdministrationController controller;

  CompanyChoiceAdministrationPresenter(this.controller);

  ///获取根部门 子部门
  Future<dynamic> getDepartmentListManager() {
    var params = <String, String>{};
    if (!TextUtil.isEmpty(controller.parentUuid.value)) {
      params["parent_uuid"] = controller.parentUuid.value;
    }
    return requestNetwork<DepartmentCompanyEntity>(Method.get, url: HttpApi.GET_COMPANY_DEPARTMENT_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        controller.data.value = data;
        controller.initMyList(data.list ?? []);
      } else {}
    }, onError: (_, __) {});
  }


}
