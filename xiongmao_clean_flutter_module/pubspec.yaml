name: xiongmao_clean_flutter_module
description: "商业保洁的 Module"

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
#
# This version is used _only_ for the Runner app, which is used if you just do
# a `flutter run` or a `flutter make-host-app-editable`. It has no impact
# on any other native host app that you embed your Flutter project into.
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'


dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  ######################################################################################################
  ######################################################################################################
  ######################################################################################################
  # flutterboost
  flutter_boost:
    git:
      url: 'https://github.com/alibaba/flutter_boost.git'
      ref: '5.0.0'
  # 贝壳的组件库
  bruno: ^3.4.3
  # TD
  tdesign_flutter: ^0.1.0
  # 网络库
  http: ^0.13.6
  dio: ^4.0.6
  # 状态管理
  provider: ^6.0.4
  # 路由框架 https://github.com/theyakka/fluro
  fluro: ^2.0.3
  # 屏幕适配
  flutter_screenutil: ^5.8.2
  # 权限申请
  permission_handler: 11.3.1
  # 图片缓存 https://github.com/renefloor/flutter_cached_network_image
  cached_network_image: ^3.3.0
  # webview组件
  webview_flutter: 4.0.5
  #生成唯一uuid
  uuid: ^3.0.7
  # 获取包各种信息
  package_info_plus: ^4.0.2
  # 获取设备信息
  device_info_plus: ^8.1.0
  # 保存到相册
  image_gallery_saver: ^2.0.2
  # 仿照微信相册
  image_pickers: 2.0.5+2
#  wechat_assets_picker: 8.6.3
  # 图片压缩
  flutter_image_compress: ^2.3.0
  # 获取系统文件路径
  path_provider: 2.0.15
  # flustars依赖于Dart常用工具类库common_utils,以及对其他第三方库封装 ，
  # 目前包含SharedPreferences Util, Screen Util, Directory Util, Widget Util, Image Util
  flustars: 2.0.1
  # log 日志
  logger: ^1.1.0
  # 跳转/状态
  get: ^4.6.5
  # 加载框
  flutter_smart_dialog: ^4.9.0+5
  # 格式化String https://github.com/Naddiseo/dart-sprintf
  sprintf: ^7.0.0
  # 界面之间的数据传递
  event_bus: ^2.0.0
  # 七牛云上传
  qiniu_flutter_sdk: ^0.4.0
  # 分享
  share_plus: ^7.2.2
  # 选择本地文件
  file_picker: ^5.3.1
  #拨号跳转
  url_launcher: ^6.0.15 # 确保使用最新版本
  #本地持久化
  shared_preferences: ^2.0.0
  #软键盘收起
  keyboard_actions: ^4.1.0



  ######################################################################################################
  ######################################################################################################
  ######################################################################################################

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add Flutter specific assets to your application, add an assets section,
  # like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add Flutter specific custom fonts to your application, add a fonts
  # section here, in this "flutter" section. Each entry in this list should
  # have a "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


  # This section identifies your Flutter project as a module meant for
  # embedding in a native host app.  These identifiers should _not_ ordinarily
  # be changed after generation - they are used to ensure that the tooling can
  # maintain consistency when adding or modifying assets and plugins.
  # They also do not have any bearing on your native host application's
  # identifiers, which may be completely independent or the same as these.

  assets:
    - assets/images/
    - assets/images/state/
    - assets/images/base/
    - assets/images/common/

  module:
    androidX: true
    androidPackage: com.business.clean.xiongmao_clean_flutter_module
    iosBundleIdentifier: com.business.clean.xiongmaoCleanFlutterModule
